package com.enosisbd.app.controller.auth;

import com.enosisbd.app.controller.exception.ProvideValidGitDataException;
import com.enosisbd.app.util.GitProviderUtils;
import com.enosisbd.app.util.GitProviderUtils.GitProvider;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.net.URI;

/**
 * Controller for handling authentication related operations
 */
@Controller
@RequestMapping("/auth")
public class AuthenticationController implements AuthenticationApi {

    private static final String GIT_REPO_URL_SESSION_KEY = "gitRepositoryUrl";
    private static final String GIT_USER_EMAIL_SESSION_KEY = "gitUserEmail";
    private static final String ORIGINAL_URL_SESSION_KEY = "originalUrl";

    @Value("${spring.security.oauth2.client.registration.github.client-id}")
    private String githubClientId;

    @Value("${spring.security.oauth2.client.registration.bitbucket.client-id}")
    private String bitbucketClientId;

    @Value("${spring.security.oauth2.client.provider.github.authorization-uri}")
    private String githubAuthUri;

    @Value("${spring.security.oauth2.client.provider.bitbucket.authorization-uri}")
    private String bitbucketAuthUri;

    /**
     * Initiates the OAuth2 login flow based on the Git repository URL
     *
     * @param gitRepositoryUrl the Git repository URL
     * @param gitUserEmail     the Git user email
     * @param request          the HTTP request
     * @return a redirect to the appropriate OAuth2 provider
     */
    @GetMapping("/login")
    @Override
    public ResponseEntity<String> initiateLogin(
            @RequestParam(required = true) String gitRepositoryUrl,
            @RequestParam(required = true) String gitUserEmail,
            @RequestParam(required = false) String redirectUrl,
            HttpServletRequest request) throws ProvideValidGitDataException {


        if ((gitRepositoryUrl == null || gitRepositoryUrl.isEmpty()) || (gitUserEmail == null || gitUserEmail.isEmpty())) {
            throw new ProvideValidGitDataException("Provide valid git data exception.");
        }

        // Store repository URL and email in session for later use
        HttpSession session = request.getSession(true);
        session.setAttribute(GIT_REPO_URL_SESSION_KEY, gitRepositoryUrl);
        session.setAttribute(GIT_USER_EMAIL_SESSION_KEY, gitUserEmail);

        // Store the original URL if provided, otherwise default to /container/start
        if (redirectUrl != null && !redirectUrl.isEmpty()) {
            session.setAttribute(ORIGINAL_URL_SESSION_KEY, redirectUrl);
        } else {
            session.setAttribute(ORIGINAL_URL_SESSION_KEY, "/container/start");
        }

        // Determine which OAuth2 provider to use based on the repository URL
        GitProvider provider = GitProviderUtils.determineProvider(gitRepositoryUrl);

        String redirectUri;
        if (provider == GitProvider.GITHUB) {
            redirectUri = "/oauth2/authorization/github";
        } else if (provider == GitProvider.BITBUCKET) {
            redirectUri = "/oauth2/authorization/bitbucket";
        } else {
            return ResponseEntity.badRequest().body("Unsupported Git provider in URL: " + gitRepositoryUrl);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setLocation(URI.create(redirectUri));
        return new ResponseEntity<>(headers, HttpStatus.FOUND);
    }

    /**
     * Handles the OAuth2 callback and redirects to the original URL
     */
    @GetMapping("/callback")
    @Override
    public ResponseEntity<String> handleCallback(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        String redirectUrl = "/container/start"; // Default

        if (session != null && session.getAttribute(ORIGINAL_URL_SESSION_KEY) != null) {
            redirectUrl = (String) session.getAttribute(ORIGINAL_URL_SESSION_KEY);
            session.removeAttribute(ORIGINAL_URL_SESSION_KEY);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setLocation(URI.create(redirectUrl));
        return new ResponseEntity<>(headers, HttpStatus.FOUND);
    }
}
