package com.enosisbd.api.server.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.FORBIDDEN)
public class ForbiddenRestException extends RuntimeException {
    public ForbiddenRestException(String message) {
        super(message);
    }

    public static ForbiddenRestException with(String message) {
        return new ForbiddenRestException(message);
    }
}
