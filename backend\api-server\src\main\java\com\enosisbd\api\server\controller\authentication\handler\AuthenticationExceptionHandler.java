package com.enosisbd.api.server.controller.authentication.handler;

import com.enosisbd.api.server.controller.authentication.exceptions.UserIsNotActiveException;
import com.enosisbd.api.server.controller.authentication.exceptions.UsernameAlreadyExistsException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

@RestControllerAdvice
public class AuthenticationExceptionHandler {

    @ExceptionHandler(UsernameAlreadyExistsException.class)
    public ResponseEntity<?> handleUsernameAlreadyExists(UsernameAlreadyExistsException ex) {
        Map<String, String> response = new HashMap<>();
        response.put("error", "Username already exists");
        response.put("message", ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(response);
    }

    @ExceptionHandler(UserIsNotActiveException.class)
    public ResponseEntity<Map<String, Object>> handleUserIsNotActiveException(UserIsNotActiveException ex) {
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("status", "error");
        responseMap.put("message", ex.getMessage());
        return new ResponseEntity<>(responseMap, HttpStatus.FORBIDDEN);
    }
}
