// index.ts
import dotenv from "dotenv";
import { Router } from "express";
import { WebCrawlerPool } from "../services/web-crawler-pool.service";
import { CrawlRequest } from "../types/web-crawler.types";
import logger from "../utils/logger";
import codeRouter from "./code";

dotenv.config();

const router = Router();

// Get configuration from environment variables with defaults
const MAX_CONCURRENCY = parseInt(process.env.MAX_CONCURRENT_CRAWLS || "10", 10);
const REQUEST_TIMEOUT = parseInt(
  process.env.CRAWLER_REQUEST_TIMEOUT || "120000",
  10
);
const MAX_QUEUE_SIZE = parseInt(process.env.CRAWLER_QUEUE_SIZE || "100", 10);
const HEADLESS = process.env.CRAWLER_HEADLESS !== "false";

// Create crawler pool with configuration
const crawlerPool = new WebCrawlerPool({
  maxConcurrency: MAX_CONCURRENCY,
  requestTimeout: REQUEST_TIMEOUT,
  maxQueueSize: MAX_QUEUE_SIZE,
  headless: HEADLESS,
});

// Mount the code router
router.use(codeRouter);

// GET endpoint for simple crawling
router.get("/scraper/scrape", async (req, res) => {
  const url = req.query.url as string;
  const pageName = req.query.pageName as string;

  if (!url) {
    logger.warn("Crawl request missing URL");
    return res.status(400).json({ error: "URL parameter is required" });
  }

  try {
    logger.info("Starting crawl:", { url });
    const result = await crawlerPool.crawl(url, pageName);
    logger.info("Crawl completed successfully:", {
      url,
      elementsFound: result.el.length,
    });
    res.json(result);
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    logger.error("Error during crawl:", { url, error: errorMessage });
    return res.status(500).json({ error: errorMessage });
  }
});

// POST endpoint for advanced crawling with authentication
router.post("/scraper/scrape", async (req, res) => {
  try {
    const { url, pageName, auth, options } = req.body as CrawlRequest;
    if (!url) {
      logger.warn("Crawl request missing URL");
      return res.status(400).json({
        error: "URL is required in request body",
      });
    }

    // Validate auth config if provided
    if (auth) {
      // Validate credentials if provided
      if (auth.credentials) {
        if (
          !auth.credentials.usernameSelector ||
          !auth.credentials.passwordSelector ||
          !auth.credentials.submitSelector
        ) {
          return res.status(400).json({
            error:
              "Login credentials must include usernameSelector, passwordSelector, and submitSelector",
          });
        }
      }

      // Validate cookies if provided
      if (auth.cookies && !Array.isArray(auth.cookies)) {
        return res.status(400).json({
          error: "Cookies must be an array",
        });
      }

      // Validate localStorage if provided
      if (auth.localStorage && !Array.isArray(auth.localStorage)) {
        return res.status(400).json({
          error: "localStorage must be an array",
        });
      }

      // Validate bearer token if provided
      if (auth.bearerToken && typeof auth.bearerToken !== "string") {
        return res.status(400).json({
          error: "bearerToken must be a string",
        });
      }
    }

    logger.info("Starting crawl:", {
      url,
      hasCredentials: !!auth?.credentials,
      hasCookies: !!auth?.cookies,
      hasLocalStorage: !!auth?.localStorage,
      hasBearerToken: !!auth?.bearerToken,
    });

    const result = await crawlerPool.crawl(url.toString(), pageName, auth, options);
    logger.info("Crawl completed successfully:", {
      url,
      elementsFound: result.el.length,
    });

    res.json({ ...result });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    logger.error("Error during crawl:", { error: errorMessage });
    return res.status(500).json({ error: errorMessage });
  }
});

// Add status endpoint for monitoring
router.get("/scraper/status", (_, res) => {
  const status = crawlerPool.getQueueStatus();
  res.json({
    ...status,
    totalCapacity: MAX_CONCURRENCY,
    config: {
      maxConcurrency: MAX_CONCURRENCY,
      requestTimeout: REQUEST_TIMEOUT,
      maxQueueSize: MAX_QUEUE_SIZE,
      headless: HEADLESS,
    },
  });
});

router.get("/health", (_, res) => {
  logger.debug("Health check requested");
  res.json({ status: "healthy" });
});

// Handle graceful shutdown
process.on("SIGTERM", () => {
  logger.info("SIGTERM received, shutting down crawler pool");
  crawlerPool.shutdown().then(() => {
    logger.info("Crawler pool shut down successfully");
  });
});

export default router;
