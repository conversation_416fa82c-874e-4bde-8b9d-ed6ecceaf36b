package com.enosisbd.api.server.service.user;

import com.enosisbd.api.server.dto.*;
import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.entity.UserPreference;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

public interface UserService {
    Optional<User> findByEmail(String email);
    User persist(User user);
    ResponseEntity<List<UserDTO>> listNonAdminUser();
    // Profile operations
    UserProfileDTO getCurrentUserProfile();
    UserProfileDTO updateUserProfile(UserProfileDTO profileDTO);
    ResponseEntity<?> changePassword(PasswordChangeDTO passwordChangeDTO);
    // Preference operations
    UserPreferenceDTO getUserPreference();
    UserPreferenceDTO updateUserPreference(UserPreferenceDTO preferenceDTO);
    List<User> searchUsersForSharing(String query, Long currentUserId, String entityType, Long entityId);
}
