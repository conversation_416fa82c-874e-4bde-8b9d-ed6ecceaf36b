package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.EntityAccess;
import com.enosisbd.api.server.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntityAccessRepository extends JpaRepository<EntityAccess, Long> {

    boolean existsBySharedWithUserIdAndEntityTypeIdAndEntityId(Long sharedWithUserId, Long entityTypeId, Long entityId);

    @Modifying
    @Query("DELETE FROM EntityAccess ea WHERE ea.sharedWithUser.id = :sharedWithUserId AND ea.entityType.id = :entityTypeId AND ea.entityId = :entityId")
    void deleteBySharedWithUserIdAndEntityTypeIdAndEntityId(
            @Param("sharedWithUserId") Long sharedWithUserId,
            @Param("entityTypeId") Long entityTypeId,
            @Param("entityId") Long entityId);

    @Query("SELECT DISTINCT ea.sharedWithUser FROM EntityAccess ea WHERE ea.entityType.id = :entityTypeId AND ea.entityId = :entityId")
    List<User> findUsersWithAccessByEntityTypeIdAndEntityId(
            @Param("entityTypeId") Long entityTypeId,
            @Param("entityId") Long entityId);

    @Query("SELECT DISTINCT ea.sharedWithUser FROM EntityAccess ea WHERE ea.entityType.id = :entityTypeId AND ea.entityId = :entityId AND ea.isInherited = false")
    List<User> findUsersWithDirectAccessByEntityTypeIdAndEntityId(
            @Param("entityTypeId") Long entityTypeId,
            @Param("entityId") Long entityId);

    @Query("SELECT ea.entityId FROM EntityAccess ea WHERE ea.sharedWithUser.id = :userId AND ea.entityType.id = :entityTypeId")
    List<Long> findEntityIdsByUserIdAndEntityTypeId(
            @Param("userId") Long userId,
            @Param("entityTypeId") Long entityTypeId);

    @Query("SELECT ea.entityId FROM EntityAccess ea WHERE ea.sharedWithUser.id = :userId AND ea.entityType.id = :entityTypeId AND ea.isInherited = false")
    List<Long> findDirectAccessEntityIdsByUserIdAndEntityTypeId(
            @Param("userId") Long userId,
            @Param("entityTypeId") Long entityTypeId);

    @Query("SELECT CASE WHEN COUNT(ea) > 0 THEN true ELSE false END FROM EntityAccess ea " +
           "WHERE ea.sharedWithUser.id = :userId AND ea.entityType.id = :entityTypeId AND ea.entityId = :entityId AND ea.isInherited = false")
    boolean existsBySharedWithUserIdAndEntityTypeIdAndEntityIdAndNotInherited(
            @Param("userId") Long userId,
            @Param("entityTypeId") Long entityTypeId,
            @Param("entityId") Long entityId);

    @Query("SELECT ea FROM EntityAccess ea " +
           "WHERE ea.sharedWithUser.id = :sharedWithUserId AND ea.entityType.id = :entityTypeId AND ea.entityId = :entityId")
    EntityAccess findBySharedWithUserIdAndEntityTypeIdAndEntityId(
            @Param("sharedWithUserId") Long sharedWithUserId,
            @Param("entityTypeId") Long entityTypeId,
            @Param("entityId") Long entityId);
}
