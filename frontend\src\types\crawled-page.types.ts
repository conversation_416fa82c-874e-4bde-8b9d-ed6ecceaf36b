export interface CrawlOption {
  skipTags?: string[];
  skipCssSelectors?: string[];
  skipHidden: boolean;
  onlyTags?: string[];
  onlyCssSelectors?: string[];
  needsLogin?: boolean;
}

export interface CrawledPage {
  id: string | number;
  createdAt?: string;
  updatedAt?: string;
  pageName: string;
  pageUrl: string;
  crawlOption: CrawlOption;
  domJson?: Record<string, unknown>;
  projectId?: number;
  isCrawled?: boolean;
}

