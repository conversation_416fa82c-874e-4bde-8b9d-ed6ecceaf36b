package com.enosisbd.api.server.service.token.impl;

import java.time.Instant;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.enosisbd.api.server.model.ProviderType;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.stereotype.Service;

import com.enosisbd.api.server.service.token.JwtTokenService;
import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;

@Service
public class JwtTokenServiceImpl implements JwtTokenService {

    private static final int EXPIRATION_TIME = 60 * 5;  // 5 minutes
    private static final int REFRESH_TOKEN_EXPIRATION = 7 * 24 * 60 * 60; // 7 days
    private final RSAKey rsaKey;
    private final JwtDecoder jwtDecoder;
    
    @Value("${app.backend.url}")
    private String backendUrl;

    public JwtTokenServiceImpl(RSAKey rsaKey, JwtDecoder jwtDecoder) {
        this.rsaKey = rsaKey;
        this.jwtDecoder = jwtDecoder;
    }

    public String generateAccessToken(String subject, List<String> roles, String authProvider) throws JOSEException {
        JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                .subject(subject)
                .claim("roles", roles)
                .issuer(backendUrl)
                .claim("tokenType", "access")
                .claim("authProvider", authProvider)
                .expirationTime(Date.from(Instant.now().plusSeconds(EXPIRATION_TIME)))
                .build();
        JWSHeader header = new JWSHeader.Builder(JWSAlgorithm.RS256)
                .keyID(rsaKey.getKeyID())
                .type(JOSEObjectType.JWT)
                .build();
        SignedJWT signedJWT = new SignedJWT(header, claimsSet);
        JWSSigner signer = new RSASSASigner(rsaKey.toPrivateKey());
        signedJWT.sign(signer);
        return signedJWT.serialize();
    }

    @Override
    public boolean validateRefreshToken(String token) {
        try {
            Jwt decodedToken = jwtDecoder.decode(token);
            Instant expiration = decodedToken.getExpiresAt();
            return expiration == null || !expiration.isBefore(Instant.now());
        } catch (JwtException | IllegalArgumentException e) {
            return false;
        }
    }

    @Override
    public String getAuthProvider(String token) {
        Jwt jwt = jwtDecoder.decode(token);
        String authProvider = (String) jwt.getClaims().getOrDefault("authProvider", ProviderType.LOCAL.getName());
        return authProvider;
    }

    @Override
    public String extractUsername(String token) {
        Jwt jwt = jwtDecoder.decode(token);
        return jwt.getSubject();
    }



    public String generateRefreshToken(String subject, List<String> roles, String authProvider) throws JOSEException {
        JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                .subject(subject)
                .issuer(backendUrl)
                .expirationTime(Date.from(Instant.now().plusSeconds(REFRESH_TOKEN_EXPIRATION)))
                .claim("roles", roles)
                .claim("tokenType", "refresh")
                .claim("authProvider", authProvider)
                .build();
        JWSHeader header = new JWSHeader.Builder(JWSAlgorithm.RS256)
                .keyID(rsaKey.getKeyID())
                .type(JOSEObjectType.JWT)
                .build();
        SignedJWT signedJWT = new SignedJWT(header, claimsSet);
        JWSSigner signer = new RSASSASigner(rsaKey.toPrivateKey());
        signedJWT.sign(signer);
        return signedJWT.serialize();
    }
}
