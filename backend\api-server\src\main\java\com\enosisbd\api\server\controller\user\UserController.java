package com.enosisbd.api.server.controller.user;

import com.enosisbd.api.server.dto.*;
import com.enosisbd.api.server.service.user.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Log4j2
@RestController
@Tag(name = "User Operations")
@RequestMapping("/api/users")
@RequiredArgsConstructor
public class UserController implements UserApi {

    private final UserService userService;

    @Override
    @GetMapping("/list")
    public ResponseEntity<List<UserDTO>> list() {
        return userService.listNonAdminUser();
    }

    @GetMapping("/profile")
    @Operation(summary = "Get current user profile")
    public UserProfileDTO getCurrentUserProfile() {
        return userService.getCurrentUserProfile();
    }

    @PutMapping("/profile")
    @Operation(summary = "Update current user profile")
    public UserProfileDTO updateUserProfile(@Valid @RequestBody UserProfileDTO profileDTO) {
        return userService.updateUserProfile(profileDTO);
    }

    @PostMapping("/change-password")
    @Operation(summary = "Change user password")
    public ResponseEntity<?> changePassword(@Valid @RequestBody PasswordChangeDTO passwordChangeDTO) {
        return userService.changePassword(passwordChangeDTO);
    }

    @GetMapping("/preferences")
    @Operation(summary = "Get user preferences")
    public UserPreferenceDTO getUserPreferences() {
        return userService.getUserPreference();
    }

    @PutMapping("/preferences")
    @Operation(summary = "Update user preferences")
    public UserPreferenceDTO updateUserPreferences(@Valid @RequestBody UserPreferenceDTO preferenceDTO) {
        return userService.updateUserPreference(preferenceDTO);
    }
}
