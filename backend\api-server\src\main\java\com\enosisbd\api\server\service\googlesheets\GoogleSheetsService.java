package com.enosisbd.api.server.service.googlesheets;

import com.enosisbd.api.server.dto.SheetTestCaseDto;
import com.enosisbd.api.server.dto.SubModuleRangeDto;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.List;
import java.util.Map;

/**
 * Service for interacting with Google Sheets API
 */
public interface GoogleSheetsService {

    /**
     * Extract the sheet ID from a Google Sheets URL
     *
     * @param sheetUrl The Google Sheets URL
     * @return The sheet ID
     */
    String extractSheetId(String sheetUrl);

    /**
     * Get the sheet names from a Google Sheet
     *
     * @param sheetId The Google Sheet ID
     * @param userEmail Optional user email for access control (if using OAuth)
     * @return A list of sheet names
     * @throws IOException If there is an error accessing the sheet
     * @throws GeneralSecurityException If there is a security error
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    List<String> getSheetNames(String sheetId, String userEmail) throws IOException, GeneralSecurityException;

    /**
     * Get the Google Sheet file name
     *
     * @param sheetId The Google Sheet ID
     * @param userEmail Optional user email for access control (if using OAuth)
     * @return The name of the Google Sheet file
     * @throws IOException If there is an error accessing the sheet
     * @throws GeneralSecurityException If there is a security error
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    String getSheetFileName(String sheetId, String userEmail) throws IOException, GeneralSecurityException;

    /**
     * Get the sheet data from a Google Sheet
     *
     * @param sheetId The Google Sheet ID
     * @param sheetName The name of the sheet to get data from
     * @param userEmail Optional user email for access control (if using OAuth)
     * @return A list of rows, where each row is a list of cell values
     * @throws IOException If there is an error accessing the sheet
     * @throws GeneralSecurityException If there is a security error
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    List<List<Object>> getSheetData(String sheetId, String sheetName, String userEmail) throws IOException, GeneralSecurityException;

    /**
     * Extract submodules from a sheet based on a column and starting row
     *
     * @param sheetData The sheet data
     * @param columnIndex The index of the column to extract submodules from (0-based)
     * @param startRow The starting row to extract submodules from (0-based)
     * @return A list of SubModuleRangeDto objects
     */
    List<SubModuleRangeDto> extractSubmodules(List<List<Object>> sheetData, int columnIndex, int startRow);

    /**
     * Convert a column letter to a 0-based index
     *
     * @param column The column letter (e.g., "A", "B", "C")
     * @return The 0-based column index
     */
    int columnLetterToIndex(String column);

    /**
     * Check if the current user has access to the Google Sheet
     *
     * @param sheetId The Google Sheet ID
     * @param userEmail The user's email address
     * @return True if the user has access, false otherwise
     * @throws IOException If there is an error accessing the sheet
     * @throws GeneralSecurityException If there is a security error
     */
    boolean hasAccessToSheet(String sheetId, String userEmail) throws IOException, GeneralSecurityException;

    /**
     * Get test cases from a Google Sheet based on specific columns and row range
     *
     * @param sheetId The Google Sheet ID
     * @param sheetName The name of the sheet to get data from
     * @param startRow The starting row (0-based)
     * @param endRow The ending row (0-based)
     * @param caseIdColumn The column letter for case IDs
     * @param descriptionColumn The column letter for descriptions
     * @param expectedResultColumn The column letter for expected results
     * @param userEmail Optional user email for access control (if using OAuth)
     * @return A list of SheetTestCaseDto objects
     * @throws IOException If there is an error accessing the sheet
     * @throws GeneralSecurityException If there is a security error
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    List<SheetTestCaseDto> getTestCases(
            String sheetId,
            String sheetName,
            int startRow,
            int endRow,
            String caseIdColumn,
            String descriptionColumn,
            String expectedResultColumn,
            String userEmail) throws IOException, GeneralSecurityException;
}
