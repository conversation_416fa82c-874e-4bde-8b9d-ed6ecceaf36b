import React from 'react';
import { CardViewIcon, ListViewIcon } from '../icons/IconLibrary';
import './ViewToggle.css';

export type ViewMode = 'card' | 'table';

interface ViewToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({ viewMode, onViewModeChange }) => {
  return (
    <div className="view-toggle">
      <button
        type="button"
        className={`toggle-btn ${viewMode === 'card' ? 'active' : ''}`}
        onClick={() => onViewModeChange('card')}
        aria-label="Card View"
        title="Card View"
      >
        <CardViewIcon />
      </button>
      <button
        type="button"
        className={`toggle-btn ${viewMode === 'table' ? 'active' : ''}`}
        onClick={() => onViewModeChange('table')}
        aria-label="Table View"
        title="Table View"
      >
        <ListViewIcon />
      </button>
    </div>
  );
};
