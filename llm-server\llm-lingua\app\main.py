from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from llmlingua import PromptCompressor
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

llm_lingua = None

app = FastAPI(
    title="Prompt Compressor API",
    description="A FastAPI service to compress prompts using LLMLingua.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

async def initialize_llm_lingua():
    global llm_lingua
    logger.info("Downloading and initializing LLMLingua model...")
    llm_lingua = PromptCompressor(
        model_name='microsoft/llmlingua-2-xlm-roberta-large-meetingbank', # Fine Tuned Model For Prompt Compression
        use_llmlingua2=True
    )
    logger.info("LLMLingua model initialized successfully.")

class PromptCompressRequest(BaseModel):
    input_prompt: str
    rate: float = 0.8

class PromptCompressResponse(BaseModel):
    output_prompt: str

@app.post("/prompt/compress", response_model=PromptCompressResponse, tags=["Prompt Compression"])
async def compress_text(request: PromptCompressRequest):
    try:
        if llm_lingua is None:
            raise HTTPException(status_code=503, detail="LLMLingua model is not yet initialized.")
        logger.info(f"Received request to compress prompt: {request.input_prompt}")
        compressed_text = llm_lingua.compress_prompt_llmlingua2(
            request.input_prompt,
            rate=request.rate,
            target_token=-1,
            use_context_level_filter=True,
            context_level_rate=1.0,
            context_level_target_token=-1,
            return_word_label=True,
            label_sep=" ",
            token_to_word="mean",
            force_reserve_digit=True,
            force_tokens=['\n', '.', '!', '?', ','],
            chunk_end_tokens=['.', '\n'],
            drop_consecutive=True
        )
        logger.info(f"Compressed prompt : \n {compressed_text}")
        if isinstance(compressed_text, dict):
            compressed_text = compressed_text.get("compressed_prompt", "")
        return PromptCompressResponse(output_prompt=compressed_text)

    except Exception as e:
        logger.error(f"Error compressing prompt: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.on_event("startup")
async def startup_event():
    logger.info("Application Started Event and Invoking ASYNC Download.")
    await initialize_llm_lingua()
