import winston from 'winston';
import path from 'path';
import fs from 'fs/promises';
import 'winston-daily-rotate-file';

const APP_NAME = 'playwright-crawler';

// Ensure logs directory exists
const ensureLogsDir = async () => {
  const logsDir = path.join(process.cwd(), 'logs');
  try {
    await fs.access(logsDir);
  } catch (error) {
    try {
      await fs.mkdir(logsDir, { recursive: true });
      console.log(`Created logs directory at: ${logsDir}`);
    } catch (mkdirError) {
      console.error(`Failed to create logs directory: ${mkdirError}`);
      // Fallback to a temporary directory if we can't create the logs directory
      return path.join(require('os').tmpdir(), 'playwright-crawler-logs');
    }
  }
  return logsDir;
};

// Initialize logger asynchronously
const initializeLogger = async () => {
  const logsDir = await ensureLogsDir();

  // Create a more compact format for console output
  const consoleFormat = winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss.SSS'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, clientIp, requestId, stack, ...meta }) => {
      // Pad the level to a consistent width (5 characters)
      const paddedLevel = level.toUpperCase().padEnd(5);
      const base = `${timestamp} [${paddedLevel}] [${meta.service || APP_NAME}]`;
      const tracking = clientIp || requestId ? 
        `[${clientIp || 'UNKNOWN'}][${requestId || 'SYSTEM'}]` : '';
      const msg = `${base}${tracking ? ' ' + tracking : ''} ${message}`;
      const stackTrace = stack ? `\n${stack}` : '';
      const metadata = Object.keys(meta).length > 1 ? `\n${JSON.stringify(meta, null, 2)}` : ''; // > 1 because meta always has 'service'
      return `${msg}${stackTrace}${metadata}`;
    })
  );

  // Keep the existing format for file logging
  const fileFormat = winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss.SSS'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, clientIp, requestId, stack, ...meta }) => {
      const base = `${timestamp} ${level.toUpperCase()} [${meta.service || APP_NAME}]`;
      const tracking = `[IP: ${clientIp || 'UNKNOWN'}] [ReqID: ${requestId || 'SYSTEM'}]`;
      const msg = `${base} ${tracking} : ${message}`;
      const stackTrace = stack ? `\n${stack}` : '';
      const metadata = Object.keys(meta).length ? `\n${JSON.stringify(meta, null, 2)}` : '';
      return `${msg}${stackTrace}${metadata}`;
    })
  );

  return winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    defaultMeta: { service: APP_NAME },
    transports: [
      // Console transport with compact format
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize({ message: true }), // Only colorize the message, not the entire output
          consoleFormat
        )
      }),
      
      // File transport with detailed format
      new winston.transports.DailyRotateFile({
        filename: path.join(logsDir, `${APP_NAME}-error-%DATE%.log`),
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        maxFiles: '10d',
        maxSize: '10m',
        format: fileFormat
      })
    ]
  });
};

// Create a temporary logger for use during initialization
let logger = winston.createLogger({
  transports: [new winston.transports.Console()]
});

// Initialize the real logger
initializeLogger().then(initializedLogger => {
  logger = initializedLogger;
}).catch(error => {
  console.error('Failed to initialize logger:', error);
});

// Request context management
export const requestContext = {
  requestId: '',
  clientIp: ''
};

export const setRequestContext = (requestId: string, clientIp: string) => {
  requestContext.requestId = requestId;
  requestContext.clientIp = clientIp;
};

export const clearRequestContext = () => {
  requestContext.requestId = '';
  requestContext.clientIp = '';
};

export interface LogMeta {
  [key: string]: unknown;
}

export const log = {
  error: (message: string, meta?: unknown) => {
    const formattedMeta = formatErrorMeta(meta);
    logger.error(message, { ...formattedMeta, ...requestContext });
  },
  warn: (message: string, meta?: unknown) => {
    const formattedMeta = formatErrorMeta(meta);
    logger.warn(message, { ...formattedMeta, ...requestContext });
  },
  info: (message: string, meta?: unknown) => {
    const formattedMeta = formatErrorMeta(meta);
    logger.info(message, { ...formattedMeta, ...requestContext });
  },
  debug: (message: string, meta?: unknown) => {
    const formattedMeta = formatErrorMeta(meta);
    logger.debug(message, { ...formattedMeta, ...requestContext });
  }
};

function formatErrorMeta(meta: unknown): LogMeta {
  if (meta instanceof Error) {
    return {
      error: meta.message,
      stack: meta.stack
    };
  }
  if (typeof meta === 'object' && meta !== null) {
    return meta as LogMeta;
  }
  return { data: String(meta) };
}

export default log;
