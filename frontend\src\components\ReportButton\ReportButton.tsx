import React from 'react';
import './ReportButton.css';

// Generic type that can represent any entity
type Entity = Record<string, any>;

type ReportButtonProps<T extends Entity> = {
  // The entity object (version, etc.)
  entity: T;
  // The type of entity (used for button title)
  entityType: string;
  // Optional property name to display in title
  entityNameProperty?: string;
  // Callback function
  onReport: (entity: T, e: React.MouseEvent) => void;
  // Optional class name for additional styling
  className?: string;
};

const ReportButton = <T extends Entity>({
  entity,
  entityType,
  entityNameProperty,
  onReport,
  className = '',
}: ReportButtonProps<T>) => {
  // Get entity name for title if entityNameProperty is provided
  const getEntityName = () => {
    if (!entityNameProperty) return '';

    // Handle nested properties with dot notation (e.g., "project.name")
    const properties = entityNameProperty.split('.');
    let value = entity;

    for (const prop of properties) {
      if (value && typeof value === 'object' && prop in value) {
        value = value[prop];
      } else {
        return '';
      }
    }

    return value ? ` "${value}"` : '';
  };

  return (
    <div className={`report-button-container ${className}`} onClick={(e) => e.stopPropagation()}>
      <button
        className="action-button report"
        onClick={(e) => onReport(entity, e)}
        title={`View Report for ${entityType}${getEntityName()}`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <polyline points="10 9 9 9 8 9"></polyline>
        </svg>
      </button>
    </div>
  );
};

export default ReportButton;
