package com.enosisbd.api.server.controller.module;

import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.dto.SubModuleDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.submodule.SubModuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/modules")
@RequiredArgsConstructor
@Tag(name = "Module Controller", description = "APIs for module management")
public class ModuleController implements ModuleApi {
    private final ModuleService moduleService;
    private final SubModuleService subModuleService;

    @PostMapping
    @Operation(summary = "Create a new module")
    @ApiResponse(responseCode = "201", description = "Module created successfully")
    @Override
    public ResponseEntity<RestResponse<ModuleDto>> add(@Valid @RequestBody ModuleDto dto) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(moduleService.add(dto)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get module by ID")
    @RequiresEntityAccess(entityType = EntityType.MODULE)
    @Override
    public RestResponse<ModuleDto> getById(@PathVariable Long id) {
        return moduleService.getById(id)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Module not found with ID: " + id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update module")
    @RequiresEntityAccess(entityType = EntityType.MODULE)
    @Override
    public RestResponse<ModuleDto> update(
            @PathVariable Long id,
            @Valid @RequestBody ModuleDto dto) {
        dto.setId(id);
        return moduleService.update(dto)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Module not found with ID: " + id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete module")
    @ApiResponse(responseCode = "204", description = "Module deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.MODULE)
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        return moduleService.delete(id)
                .map(ignored -> ResponseEntity.noContent().<Void>build())
                .orElseThrow(() -> NotFoundRestException.with("Module not found with ID: " + id));
    }

    @GetMapping("/{id}/sub-modules")
    @Operation(summary = "Get submodules for a module")
    @RequiresEntityAccess(entityType = EntityType.MODULE)
    @Override
    public RestResponse<List<SubModuleDto>> findByModuleId(@PathVariable Long moduleId) {
        return RestResponse.of(subModuleService.findByModuleId(moduleId));
    }
}
