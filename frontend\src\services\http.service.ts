/// write a get request using axios
import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { COMMON_BASE_URL, CRAWLER_BASE_URL, LLM_BASE_URL } from '../../config';

// Available service types
export type ServiceType = 'crawler' | 'common' | 'llm';

// Define interface for base URLs
interface ServiceBaseUrls {
  crawler: string;
  common: string;
  llm: string;
}

// Define interface for API response
export interface ApiResponse<T> {
  data: T;
  status: number;
  statusText: string;
}

// Constants
const BASE_URLS: ServiceBaseUrls = {
  crawler: CRAWLER_BASE_URL,
  common: COMMON_BASE_URL,
  llm: LLM_BASE_URL,
};

const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
} as const;

const DEFAULT_TIMEOUT = 60000; // 60 seconds

// Create the axios instances with improved configuration
const createAxiosInstance = (baseURL: string): AxiosInstance => {
  const instance = axios.create({
    baseURL,
    headers: DEFAULT_HEADERS,
    timeout: DEFAULT_TIMEOUT,
    validateStatus: (status) => status >= 200 && status < 300, // Only accept 2xx status codes
  });

  // AccessToken 
  instance.interceptors.request.use(
    (config) => {
      // Read the latest token from localStorage on every request
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => Promise.reject(error),
  );

  // Add response interceptor for common handling
  instance.interceptors.response.use(
    (response) => response,
    (error) => Promise.reject(handleError(error)),
  );

  return instance;
};

// Enhanced error handling with more specific error types
const handleError = (error: AxiosError): Error => {
  if (error.response) {
    const status = error.response.status;
    const statusText = error.response.statusText;
    const data = error.response.data as { message?: string };

    switch (status) {
      case 400:
        return new Error(`Bad Request: ${data.message || statusText}`);
      case 401:
        return new Error('Unauthorized: Please check your credentials.');
      case 403: {
        const forbiddenError = new Error('Forbidden: Access denied.');
        (forbiddenError as any).status = 403;
        return forbiddenError;
      }
      case 404: {
        const notFoundError = new Error('Resource not found');
        (notFoundError as any).status = 404;
        return notFoundError;
      }
      case 422:
        return new Error(`Validation Error: ${data.message || statusText}`);
      case 500: {
        const serverError = new Error('Internal server error');
        (serverError as any).status = 500;
        return serverError;
      }
      case 409: {
        const serverError = new Error('This email is already registered with a password. Please use the login form instead.');
        (serverError as any).status = 409;
        return serverError;
      }
      default:
        return new Error(`Server Error (${status}): ${data.message || statusText}`);
    }
  }
  if (error.request) {
    return new Error('Network Error: No response received from server');
  }
  return new Error(`Request Error: ${error.message}`);
};

// Create instances map with lazy initialization
const instances: Partial<Record<ServiceType, AxiosInstance>> = {};
const getInstance = (serviceType: ServiceType): AxiosInstance => {
  if (!instances[serviceType]) {
    instances[serviceType] = createAxiosInstance(BASE_URLS[serviceType]);
  }
  return instances[serviceType]!;
};

// Generic request method to reduce code duplication
const request = async <T>(
  serviceType: ServiceType,
  method: string,
  url: string,
  data?: Record<string, unknown>,
  params?: Record<string, unknown>,
): Promise<T> => {
  try {
    const config: AxiosRequestConfig = { method, url, params };
    if (data) {
      config.data = data;
    }
    const response: AxiosResponse<T> = await getInstance(serviceType)(config);
    return response.data;
  } catch (error) {
    throw error instanceof Error ? error : new Error('Unknown error occurred');
  }
};

// Enhanced stream handling with timeout and error recovery
const streamRequest = async (
  url: string,
  data: Record<string, unknown>,
  onChunk: (chunk: string) => void,
  options: {
    timeout?: number;
    retries?: number;
    onError?: (error: Error) => void;
  } = {},
): Promise<void> => {
  const { timeout = 60000, retries = 3, onError } = options;
  let attempt = 0;

  const token = localStorage.getItem('token');
  const headers = {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` }),
  };


  while (attempt < retries) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('ReadableStream not supported!');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        onChunk(chunk);
      }
      return;
    } catch (error) {
      attempt++;
      if (attempt === retries) {
        const finalError = handleError(error as AxiosError);
        if (onError) {
          onError(finalError);
        }
        throw finalError;
      }
      // Wait before retrying (exponential backoff)
      await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
};

// Simplified HTTP service interface
const httpService = {
  get: <T>(serviceType: ServiceType, url: string, params?: Record<string, unknown>) =>
    request<T>(serviceType, 'GET', url, undefined, params),

  post: <T>(serviceType: ServiceType, url: string, data: Record<string, unknown>) =>
    request<T>(serviceType, 'POST', url, data),

  put: <T>(serviceType: ServiceType, url: string, data: Record<string, unknown>) =>
    request<T>(serviceType, 'PUT', url, data),

  patch: <T>(serviceType: ServiceType, url: string, data: Record<string, unknown>) =>
    request<T>(serviceType, 'PATCH', url, data),

  delete: <T>(serviceType: ServiceType, url: string) => request<T>(serviceType, 'DELETE', url),

  stream: streamRequest,
};

export default httpService;
