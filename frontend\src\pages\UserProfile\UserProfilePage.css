.user-profile-page-container {
  margin: 0 auto;
  padding: 2rem 0;
}

.user-profile-page-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Two-column layout for medium and larger screens */
@media (min-width: 768px) {
  .user-profile-page-content {
    flex-direction: row;
  }

  .user-profile-page-column {
    flex: 1;
  }
}

.user-profile-page-column {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.user-profile-page-section {
  background-color: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  padding: 1.5rem;
  border: 1px solid var(--border-color);
}

.user-profile-page-section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.75rem;
}

.user-profile-page-section h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.user-profile-page-form-group {
  margin-bottom: 1.25rem;
}

.user-profile-page-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
}

.user-profile-page-form-group input {
  width: 100%;
  padding: 0.625rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
}

.user-profile-page-readonly-input {
  background-color: var(--background-light);
  cursor: not-allowed;
}

.user-profile-page-input-help,
.user-profile-page-setting-description {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.user-profile-page-setting-description {
  margin-bottom: 1rem;
}

.user-profile-page-required {
  color: var(--error-color);
}

.user-profile-page-error {
  color: var(--error-color);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.user-profile-page-error-server {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: var(--radius-md);
  padding: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #b91c1c;
}

.user-profile-page-success {
  background-color: #ecfdf5;
  color: #065f46;
  padding: 0.75rem;
  border-radius: var(--radius-md);
  margin-bottom: 1rem;
  font-size: 0.875rem;
  border: 1px solid #a7f3d0;
}

.user-profile-page-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 1rem;
  color: var(--text-secondary);
}

/* View Mode Toggle */
.user-profile-page-view-mode-toggle {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.user-profile-page-view-mode-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: white;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.user-profile-page-view-mode-button:hover:not(:disabled) {
  border-color: var(--primary-color);
}

.user-profile-page-view-mode-button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.user-profile-page-view-mode-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.user-profile-page-setting-group {
  margin-bottom: 2rem;
}
