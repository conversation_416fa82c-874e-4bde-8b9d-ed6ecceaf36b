# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

### System Files ###
.DS_Store
Thumbs.db

### Logs ###
*.log
npm-debug.log
yarn-error.log
libpeerconnection.log
testem.log

### Build/Compiled Files ###
/dist
/tmp
/out-tsc
/bazel-out
*.class
*.py[cod]
*.jar
*.app
*.exe
*.war

### Dependency Directories ###
node_modules/

### IDE/Editor Files ###
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.history/
.editorconfig

### Visual Studio Code ###
.vscode
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

### Angular ###
.angular/

### Maven ###
target/

### Unit Test Reports ###
TEST*.xml

### Miscellaneous ###
.sass-cache/
/connect.lock
/coverage
/typings
.vite

### Large Media Files ###
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

### Docker volumes
postgres-data