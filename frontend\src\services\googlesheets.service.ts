import { RestResponse } from '../types/common.types';
import { GoogleSheetImportRequest, GoogleSheetImportResponse } from '../types/google-sheets.types';
import httpService from './http.service';

const API_ENDPOINTS = {
  GOOGLE_SHEETS_IMPORT: '/api/google-sheets/import',
};

/**
 * Service for Google Sheets integration
 */
const GoogleSheetsService = {
  /**
   * Import data from a Google Sheet
   * @param request The import request
   * @returns The import response
   */
  importFromGoogleSheet: async (
    request: RestResponse<GoogleSheetImportRequest>,
  ): Promise<GoogleSheetImportResponse> => {
    const response = await httpService.post<RestResponse<GoogleSheetImportResponse>>(
      'common',
      API_ENDPOINTS.GOOGLE_SHEETS_IMPORT,
      request,
    );
    return response.data;
  },
};

export default GoogleSheetsService;
