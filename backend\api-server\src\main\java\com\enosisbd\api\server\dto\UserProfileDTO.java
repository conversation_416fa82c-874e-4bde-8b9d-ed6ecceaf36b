package com.enosisbd.api.server.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileDTO {
    private Long id;
    
    @NotBlank(message = "Full name cannot be empty")
    @Size(min = 4, max = 255, message = "Full name must be between 4 and 255 characters")
    private String fullName;
    
    private String email; // Email is read-only, included for display purposes
}
