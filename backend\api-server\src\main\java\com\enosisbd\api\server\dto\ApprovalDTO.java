package com.enosisbd.api.server.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

public class ApprovalDTO {

    @Data
    @Jacksonized
    @Builder
    public static class AdminUserApproveDTO {
        @NotEmpty(message = "Email cannot be empty")
        @Email(message = "Email should be valid")
        private String email;
        private boolean approve;
    }

    @Data
    @Jacksonized
    @Builder
    public static class AdminUserApproveResponseDTO {
        private String message;
    }
} 