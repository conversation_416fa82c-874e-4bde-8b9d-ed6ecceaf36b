package com.enosisbd.api.server.controller.authentication;

import com.enosisbd.api.server.controller.authentication.exceptions.UserIsNotActiveException;
import com.enosisbd.api.server.controller.authentication.exceptions.UsernameAlreadyExistsException;
import com.enosisbd.api.server.dto.AuthenticationDTO.*;
import com.nimbusds.jose.JOSEException;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface AuthenticationApi {
    @PostMapping("/signIn")
    ResponseEntity<SignInResponseDTO> signIn(@Valid @RequestBody SignInDTO signInDTO) throws UserIsNotActiveException;

    @PostMapping("/signUp")
    ResponseEntity<SignUpResponseDTO> signUp(@Valid @RequestBody SignUpDTO signUpDTO) throws UsernameAlreadyExistsException;

    @PostMapping("/refreshToken")
    ResponseEntity<RefreshTokenResponseDTO> refreshToken(@Valid @RequestBody RefreshTokenDTO refreshTokenDTO) throws UsernameAlreadyExistsException, UserIsNotActiveException, JOSEException;

    @PostMapping("/forgotPassword")
    ResponseEntity<ForgotPasswordResponseDTO> forgotPassword(@Valid @RequestBody ForgotPasswordDTO forgotPasswordDTO);

    @PostMapping("/resetPassword")
    ResponseEntity<ResetPasswordResponseDTO> resetPassword(@Valid @RequestBody ResetPasswordDTO resetPasswordDTO);
}
