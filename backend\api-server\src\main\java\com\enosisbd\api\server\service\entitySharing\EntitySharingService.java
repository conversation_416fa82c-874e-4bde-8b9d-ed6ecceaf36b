package com.enosisbd.api.server.service.entitySharing;

import com.enosisbd.api.server.dto.UserDTO;
import com.enosisbd.api.server.entity.BaseEntity;
import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.model.EntityType;

import java.util.List;

/**
 * Consolidated service for all entity sharing operations.
 * This service handles sharing entities, checking access, and managing shared users.
 */
public interface EntitySharingService {

    /**
     * Share a project with a user
     * This grants access to the project and all its children (versions, test suites, etc.)
     */
    void shareProject(Long projectId, Long sharedByUserId, Long sharedWithUserId);

    /**
     * Share a module with a user
     * This grants access to the parent project, this specific module, and all children of this module
     */
    void shareModule(Long moduleId, Long sharedByUserId, Long sharedWithUserId);

    /**
     * Share a submodule with a user
     * This grants access to the parent project, parent module, this specific submodule, and all children of this submodule
     */
    void shareSubModule(Long subModuleId, Long sharedByUserId, Long sharedWithUserId);

    /**
     * Remove a user's access to a project
     * This removes access to the project and all its children
     */
    void removeProjectAccess(Long projectId, Long sharedWithUserId);

    /**
     * Remove a user's access to a module
     * This removes access to the module and all its children, but not to the parent project
     */
    void removeModuleAccess(Long moduleId, Long sharedWithUserId);

    /**
     * Remove a user's access to a submodule
     * This removes access to the submodule and all its children, but not to the parent module or project
     */
    void removeSubModuleAccess(Long subModuleId, Long sharedWithUserId);

    /**
     * Check if a user has access to a specific entity
     * This checks direct access, creator access, admin access, and inherited access
     */
    boolean hasAccess(Long userId, EntityType entityType, Long entityId);

    /**
     * Check if the current user has access to a specific entity
     */
    boolean hasAccess(BaseEntity entity, EntityType entityType);

    /**
     * Get all users who have direct non-inherited access to a specific entity
     * This only includes users with direct non-inherited access, not users with inherited access
     */
    List<User> getUsersWithDirectAccess(EntityType entityType, Long entityId);

    /**
     * Check if a user has direct non-inherited access to an entity
     * This checks if the user is the creator or has direct non-inherited access
     */
    boolean hasDirectAccess(EntityType entityType, Long entityId, Long userId);

    /**
     * Check if a user has direct non-inherited access to an entity
     * This checks if the user is the creator or has direct non-inherited access
     */
    boolean hasNonInheritedDirectAccess(EntityType entityType, Long entityId, Long userId);

    /**
     * Get all entity IDs of a specific type that a user has direct non-inherited access to
     * This includes entities the user created and entities shared directly with the user (not through inheritance)
     */
    List<Long> getNonInheritedDirectAccessEntityIds(EntityType entityType, Long userId);

    /**
     * Filter a list of entities based on user access
     * @param entities The list of entities to filter
     * @param entityType The type of entities
     * @return A filtered list containing only entities the user has access to
     */
    <T extends BaseEntity> List<T> filterAccessibleEntities(List<T> entities, EntityType entityType);

    /**
     * Get all entities of a specific type that the current user has access to
     * @param allEntities All entities of the specified type
     * @param entityType The type of entities
     * @return A list of entities the user has access to
     */
    <T extends BaseEntity> List<T> getAccessibleEntities(List<T> allEntities, EntityType entityType);

    /**
     * Check if a user is the creator of an entity
     */
    boolean isCreator(Long userId, EntityType entityType, Long entityId);

    /**
     * Get the creator of an entity
     */
    String getEntityCreator(EntityType entityType, Long entityId);

    /**
     * Check if an entity type is sharable
     */
    boolean isEntitySharable(EntityType entityType);

    /**
     * Validate if an entity type is valid and sharable
     * @param entityType The entity type to validate
     * @throws BadRequestRestException if the entity type is invalid or not sharable
     */
    void validateEntityType(EntityType entityType);

    /**
     * Share an entity with a user based on entity type
     * @param entityType The type of entity to share
     * @param entityId The ID of the entity to share
     * @param currentUserId The ID of the user sharing the entity
     * @param sharedWithUserId The ID of the user to share with
     * @return true if the entity was shared successfully
     */
    boolean shareEntityByType(EntityType entityType, Long entityId, Long currentUserId, Long sharedWithUserId);

    /**
     * Remove a user's access to an entity based on entity type
     * @param entityType The type of entity to remove access from
     * @param entityId The ID of the entity to remove access from
     * @param userId The ID of the user to remove access for
     * @return true if access was removed successfully
     */
    boolean removeAccessByType(EntityType entityType, Long entityId, Long userId);

    /**
     * Get users who have direct access to an entity and convert to DTOs
     * @param entityType The type of entity
     * @param entityId The ID of the entity
     * @return List of UserDTOs representing users with direct access
     */
    List<UserDTO> getUsersWithDirectAccessAsDto(EntityType entityType, Long entityId);

    /**
     * Search for users who can be shared with
     * @param query The search query
     * @param currentUserId The ID of the current user
     * @param entityType The type of entity (optional)
     * @param entityId The ID of the entity (optional)
     * @return List of UserDTOs representing users who can be shared with
     */
    List<UserDTO> searchUsersForSharing(String query, Long currentUserId, EntityType entityType, Long entityId);
}
