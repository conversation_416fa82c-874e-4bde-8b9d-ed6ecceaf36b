.footer {
  background-color: white;
  border-top: 1px solid var(--border-color);
  padding: 1rem 0;
  margin-top: auto;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer p {
  color: var(--text-secondary);
  font-size: var(--font-regular);
  margin: 0;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--font-regular);
  transition: color 0.2s;
}

.footer-links a:hover {
  color: var(--primary-color);
}

@media (max-width: 640px) {
  .footer-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .footer-links {
    gap: 1rem;
  }
}
