import { ArrowLeftOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { Drawer, FloatButton } from 'antd';
import React, { useEffect, useRef } from 'react';
import './CustomDrawer.css';

interface CustomDrawerProps {
  title: string;
  isOpen: boolean;
  onClose: () => void;
  onToggle: () => void;
  position: 'left' | 'right';
  width?: string;
  children: React.ReactNode;
  autoScrollToBottom?: boolean;
  isLoading?: boolean;
  showToggleButton?: boolean;
  hasStreamingStarted?: boolean;
  extraAction?: {
    show: boolean;
    onClick?: () => void;
    label?: string;
    tooltip?: string;
    disabled?: boolean;
  };
}

export const CustomDrawer: React.FC<CustomDrawerProps> = ({
  title,
  isOpen,
  onClose,
  onToggle,
  position = 'right',
  width = '45%',
  children,
  autoScrollToBottom = false,
  extraAction,
  isLoading = false,
  showToggleButton = false,
  hasStreamingStarted = false,
}) => {
  const contentRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when content changes
  useEffect(() => {
    if (isOpen && autoScrollToBottom && contentRef.current) {
      setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.scrollTop = contentRef.current.scrollHeight;
        }
      }, 50);
    }
  }, [isOpen, children, autoScrollToBottom]);

  // Determine if the Ant Drawer's loading overlay should be shown
  const showAntDrawerLoading = isLoading && !hasStreamingStarted;

  return (
    <>
      {/* Toggle Button - Visibility controlled by showToggleButton prop */}
      {!isOpen && showToggleButton && (
        <FloatButton
          onClick={onToggle}
          className={`drawer-toggle-button ${position} has-content`}
          tooltip={`Show ${title}`}
          icon={position === 'right' ? <ArrowLeftOutlined /> : <ArrowRightOutlined />}
          type="primary"
        />
      )}

      {/* Ant Design Drawer */}
      <Drawer
        title={title}
        placement={position}
        onClose={onClose}
        open={isOpen}
        width={width}
        className="custom-drawer"
        maskClosable={true}
        mask={true}
        loading={showAntDrawerLoading}
        extra={
          extraAction && extraAction.show ? (
            <button
              className="btn-primary"
              onClick={extraAction.onClick}
              disabled={extraAction.disabled}
              title={extraAction.tooltip}
            >
              {extraAction.label}
            </button>
          ) : null
        }
        afterOpenChange={(open) => {
          // Scroll to bottom when drawer opens
          if (open && autoScrollToBottom && contentRef.current) {
            setTimeout(() => {
              if (contentRef.current) {
                contentRef.current.scrollTop = contentRef.current.scrollHeight;
              }
            }, 100);
          }
        }}
      >
        <div className="drawer-content" ref={contentRef}>
          {/* Render children regardless of loading state, 
              Ant Drawer loading overlay will cover it if active */}
          {children}
        </div>
      </Drawer>
    </>
  );
};

export default CustomDrawer;
