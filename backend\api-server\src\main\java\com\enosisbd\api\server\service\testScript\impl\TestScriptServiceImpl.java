package com.enosisbd.api.server.service.testScript.impl;

import com.enosisbd.api.server.dto.TestScriptDto;
import com.enosisbd.api.server.entity.TestScript;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.SubModuleRepository;
import com.enosisbd.api.server.repository.TestScriptRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.testScript.TestScriptService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TestScriptServiceImpl implements TestScriptService {
    private final TestScriptRepository repository;
    private final SubModuleRepository subModuleRepository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<TestScriptDto> getById(Long id) {
        // Access check is handled by the @RequiresEntityAccess annotation
        return repository.findByIdJoined(id)
                .map(this::convertToDto);
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public TestScriptDto add(TestScriptDto dto) {
        TestScript entity = convertToEntity(dto);

        // Set creator
        entity.setCreatedBy(authorizationService.getCurrentUsername());

        Long subModuleId = dto.getSubModuleId();
        if (subModuleId != null) {
            // Check if user has access to the submodule's project
            var subModule = subModuleRepository
                    .findById(subModuleId)
                    .orElseThrow(() -> BadRequestRestException.with("SubModule not found with ID: " + subModuleId));

            if (!entitySharingService.hasAccess(subModule.getModule().getProject(), EntityType.PROJECT)) {
                throw new BadRequestRestException("You don't have access to this submodule's project");
            }

            entity.setSubModule(subModule);
        }

        repository.save(entity);
        return convertToDto(entity);
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<TestScriptDto> update(TestScriptDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty()) return Optional.empty();

        TestScript existingEntity = maybe.get();
        TestScript entity = convertToEntity(dto);

        // Access check is handled by the @RequiresEntityAccess annotation

        boolean isValidId = dto.getSubModuleId() != null
                && Objects.equals(dto.getSubModuleId(), existingEntity.getSubModuleId());

        if (isValidId) {
            // Check if user has access to the submodule's project
            var subModule = subModuleRepository
                    .findById(dto.getSubModuleId())
                    .orElseThrow(() -> BadRequestRestException.with("SubModule not found with ID: " + dto.getSubModuleId()));

            entity.setSubModule(subModule);
        }

        repository.save(entity);
        return Optional.of(convertToDto(entity));
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        Optional<TestScript> testScript = repository.findById(id);
        if (testScript.isEmpty()) {
            return Optional.empty();
        }

        // Access check is handled by the @RequiresEntityAccess annotation

        // Delete the test script
        repository.delete(testScript.get());
        return Optional.of(true);
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<TestScriptDto> findBySubModuleId(Long subModuleId) {
        Optional<TestScript> testScript = repository.findBySubModuleIdJoined(subModuleId);
        if (testScript.isEmpty()) {
            return Optional.empty();
        }

        return testScript.map(this::convertToDto);
    }

    private TestScriptDto convertToDto(TestScript entity) {
        TestScriptDto dto = new TestScriptDto();
        dto.setId(entity.getId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setCode(entity.getCode());
        dto.setSubModuleId(entity.getSubModuleId());
        return dto;
    }

    private TestScript convertToEntity(TestScriptDto dto) {
        return convertToEntity(new TestScript(), dto);
    }

    private TestScript convertToEntity(TestScript entity, TestScriptDto dto) {
        entity.setId(dto.getId());
        entity.setCode(dto.getCode());
        return entity;
    }
}
