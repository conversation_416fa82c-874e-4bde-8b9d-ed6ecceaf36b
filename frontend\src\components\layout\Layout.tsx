import { ReactNode } from 'react';
import { Toolbar } from './Toolbar';
import { Footer } from './Footer';
import './Layout.css';

interface LayoutProps {
  children: ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  return (
    <div className="app-layout">
      <header className="app-header">
        <Toolbar />
      </header>
      <main className="app-main">{children}</main>
      <footer className="app-footer">
        <Footer />
      </footer>
    </div>
  );
};
