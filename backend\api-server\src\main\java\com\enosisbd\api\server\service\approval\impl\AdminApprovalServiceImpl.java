package com.enosisbd.api.server.service.approval.impl;

import com.enosisbd.api.server.dto.ApprovalDTO.*;
import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.service.approval.AdminApprovalService;
import com.enosisbd.api.server.service.user.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Locale;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AdminApprovalServiceImpl implements AdminApprovalService {

    private final UserService userService;

    @Override
    public ResponseEntity<AdminUserApproveResponseDTO> approveOrUnapproved(AdminUserApproveDTO request) {
        Optional<User> userOpt = userService.findByEmail(request.getEmail());
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            if (request.isApprove()) {
                user.setApproved(true);
            } else {
                user.setApproved(false);
            }
            userService.persist(user);
            String message = request.isApprove()
                    ? String.format("%s has been approved for using automation.", request.getEmail())
                    : String.format("%s has been unapproved and cannot use automation.", request.getEmail());
            return ResponseEntity.status(HttpStatus.OK)
                    .body(AdminUserApproveResponseDTO.builder()
                            .message(message.toLowerCase(Locale.ROOT))
                            .build());
        }

        return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .build();
    }


}
