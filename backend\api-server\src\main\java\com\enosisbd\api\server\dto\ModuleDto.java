package com.enosisbd.api.server.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class ModuleDto extends BaseDto {
    @NotNull(message = "Module name cannot be null")
    @Size(min = 3, max = 255, message = "Module name must be between 3 and 255 characters")
    private String name;
    
    @NotNull(message = "Project ID cannot be null")
    private Long projectId;
}
