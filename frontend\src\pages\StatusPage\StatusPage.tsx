import React from 'react';
import { Result } from 'antd';
import { ResultStatusType } from 'antd/es/result';
import { useNavigate } from 'react-router-dom';

interface StatusPageProps {
  status: ResultStatusType;
  title?: string;
  subTitle?: string;
  extra?: React.ReactNode;
}

// Helper to get default content based on status
const getDefaultContent = (status: ResultStatusType) => {
  switch (status) {
    case 403:
      return {
        title: '403',
        subTitle: 'Sorry, you are not authorized to access this page.',
      };
    case 404:
      return {
        title: '404',
        subTitle: 'Sorry, the page you visited does not exist.',
      };
    case 500:
      return {
        title: '500',
        subTitle: 'Sorry, something went wrong on our end.',
      };
    default:
      return {
        title: String(status),
        subTitle: 'An unexpected status occurred.',
      };
  }
};

const StatusPage: React.FC<StatusPageProps> = ({ status, title, subTitle, extra }) => {
  const navigate = useNavigate();

  const defaultContent = getDefaultContent(status);

  const finalTitle = title ?? defaultContent.title;
  const finalSubTitle = subTitle ?? defaultContent.subTitle;

  const defaultExtra = (
    <button className="btn-primary" onClick={() => navigate('/')}>
      {'Back Home'}
    </button>
  );

  const finalExtra = extra === null ? null : extra ?? defaultExtra;

  return (
    <Result
      style={{
        margin: 'auto',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
      }}
      status={status}
      title={finalTitle}
      subTitle={finalSubTitle}
      extra={finalExtra}
    />
  );
};

export default StatusPage;
