@baseUrl = http://localhost:9099

### Crawl sample cookie based app
POST  {{baseUrl}}/api/scraper/scrape
Content-Type: application/json

{
    "url": "http://localhost:3000",
    "options": {
        "skipTags": [],
        "skipCssSelectors": [],
        "skipHidden": true,
        "onlyTags": [],
        "onlyCssSelectors": []
    }
}

### Crawl infoNet login page
POST  {{baseUrl}}/api/scraper/scrape
Content-Type: application/json

{
    "url": "https://************:44344/Auth"
}

### Crawl infoNet home page
POST  {{baseUrl}}/api/scraper/scrape
Content-Type: application/json

{
    "url": "https://************/ProductClassification",
    "auth": {
        "credentials": {
            "url": "https://************/Auth",
            "usernameSelector": "#codeInputControl",
            "passwordSelector": "#passwordInputControl",
            "submitSelector": "[type='submit']",
            "username": "x",
            "password": "x"
        }
    }
}