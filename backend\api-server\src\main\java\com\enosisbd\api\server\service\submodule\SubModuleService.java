package com.enosisbd.api.server.service.submodule;

import com.enosisbd.api.server.dto.SheetTestCasesResponseDto;
import com.enosisbd.api.server.dto.SubModuleDto;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.List;
import java.util.Optional;

public interface SubModuleService {
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    List<SubModuleDto> findAll();

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    Optional<SubModuleDto> getById(Long id);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    SubModuleDto add(SubModuleDto dto);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    Optional<SubModuleDto> update(SubModuleDto dto);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    Optional<Boolean> delete(Long id);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    boolean existsById(Long id);

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    List<SubModuleDto> findByModuleId(Long moduleId);

    /**
     * Generate test cases for a submodule from Google Sheets
     *
     * @param submoduleId The ID of the submodule
     * @return A response containing the generated test cases
     * @throws IOException If there is an error accessing the sheet
     * @throws GeneralSecurityException If there is a security error
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    SheetTestCasesResponseDto generateTestCases(Long submoduleId) throws IOException, GeneralSecurityException;
}
