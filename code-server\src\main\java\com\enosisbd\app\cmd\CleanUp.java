package com.enosisbd.app.cmd;

import java.io.File;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class CleanUp implements CommandLineRunner {

  @Value("${app.scripts.cleanup-path:/usr/local/environment/clean_repos.sh}")
  private String cleanupScriptPath;

  @Override
  public void run(String... args) throws Exception {
    try {
      MDC.put("requestId", "SYSTEM");
      MDC.put("clientIp", "LOCALHOST");

      // Check if script exists
      File scriptFile = new File(cleanupScriptPath);
      if (scriptFile.exists()) {
        ProcessBuilder pb = new ProcessBuilder(
            "bash",
            cleanupScriptPath
        );
        int exitCode = pb.start().waitFor();
        if (exitCode == 0) {
          log.info("Repository cleanup completed successfully");
        } else {
          log.error("Repository cleanup failed with exit code: {}", exitCode);
        }
      }
    } catch (Exception e) {
      log.error("Error during repository cleanup: {}", e.getMessage(), e);
      throw e;
    } finally {
      MDC.clear();
    }
  }
}
