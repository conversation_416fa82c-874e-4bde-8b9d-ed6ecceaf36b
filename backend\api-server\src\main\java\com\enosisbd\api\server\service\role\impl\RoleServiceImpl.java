package com.enosisbd.api.server.service.role.impl;

import com.enosisbd.api.server.entity.Role;
import com.enosisbd.api.server.repository.RoleRepository;
import com.enosisbd.api.server.service.role.RoleService;
import jakarta.persistence.LockModeType;
import lombok.RequiredArgsConstructor;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {
    private final RoleRepository roleRepository;

    @Override
    @Lock(LockModeType.PESSIMISTIC_READ)
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public Optional<Role> findByName(String roleName) {
        return roleRepository.findByName(roleName);
    }
}
