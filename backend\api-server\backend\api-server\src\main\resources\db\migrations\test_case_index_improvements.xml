<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" 
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" 
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" 
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd 
                                       http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd 
                                       http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <!-- Add composite index on test_suite_id and status -->
    <changeSet id="test-case-index-improvements-1" author="database-optimization">
        <comment>Add composite index on test_suite_id and status for improved filtering</comment>
        <createIndex indexName="idx_test_case_suite_status" tableName="test_case">
            <column name="test_suite_id"/>
            <column name="status"/>
        </createIndex>
    </changeSet>
    
    <!-- Add composite index on test_suite_id and test_case_id -->
    <changeSet id="test-case-index-improvements-2" author="database-optimization">
        <comment>Add composite index on test_suite_id and test_case_id for improved sorting and filtering</comment>
        <createIndex indexName="idx_test_case_suite_id" tableName="test_case">
            <column name="test_suite_id"/>
            <column name="test_case_id"/>
        </createIndex>
    </changeSet>
    
    <!-- Add covering index for common query patterns -->
    <changeSet id="test-case-index-improvements-3" author="database-optimization">
        <comment>Add covering index for common query patterns</comment>
        <createIndex indexName="idx_test_case_suite_status_id" tableName="test_case">
            <column name="test_suite_id"/>
            <column name="status"/>
            <column name="test_case_id"/>
        </createIndex>
    </changeSet>
    
    <!-- Add index on test_case_id for faster lookups and sorting -->
    <changeSet id="test-case-index-improvements-4" author="database-optimization">
        <comment>Add index on test_case_id for faster lookups and sorting</comment>
        <createIndex indexName="idx_test_case_id" tableName="test_case">
            <column name="test_case_id"/>
        </createIndex>
    </changeSet>
    
    <!-- Add GIN index on steps JSONB column if needed -->
    <changeSet id="test-case-index-improvements-5" author="database-optimization">
        <comment>Add GIN index on steps JSONB column for improved JSON content searches</comment>
        <createIndex indexName="idx_test_case_steps" tableName="test_case">
            <column name="steps" type="jsonb"/>
        </createIndex>
        <modifySql dbms="postgresql">
            <append value=" USING GIN"/>
        </modifySql>
    </changeSet>
    
</databaseChangeLog>
