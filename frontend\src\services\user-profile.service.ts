import { PasswordChange, PasswordChangeResponse, UserPreference, UserProfile } from '../types/user-profile.types';
import httpService from './http.service';

const API_ENDPOINTS = {
  PROFILE: '/api/users/profile',
  CHANGE_PASSWORD: '/api/users/change-password',
  PREFERENCES: '/api/users/preferences',
};

export const UserProfileService = {
  /**
   * Get current user profile
   */
  getCurrentUserProfile: async (): Promise<UserProfile> => {
    return httpService.get<UserProfile>('common', API_ENDPOINTS.PROFILE);
  },

  /**
   * Update user profile
   */
  updateUserProfile: async (profile: UserProfile): Promise<UserProfile> => {
    return httpService.put<UserProfile>('common', API_ENDPOINTS.PROFILE, profile as Record<string, unknown>);
  },

  /**
   * Change user password
   */
  changePassword: async (passwordData: PasswordChange): Promise<PasswordChangeResponse> => {
    return httpService.post<PasswordChangeResponse>('common', API_ENDPOINTS.CHANGE_PASSWORD, passwordData as Record<string, unknown>);
  },

  /**
   * Get user preferences
   */
  getUserPreferences: async (): Promise<UserPreference> => {
    return httpService.get<UserPreference>('common', API_ENDPOINTS.PREFERENCES);
  },

  /**
   * Update user preferences
   */
  updateUserPreferences: async (preferences: UserPreference): Promise<UserPreference> => {
    return httpService.put<UserPreference>('common', API_ENDPOINTS.PREFERENCES, preferences as Record<string, unknown>);
  },
};

export default UserProfileService;
