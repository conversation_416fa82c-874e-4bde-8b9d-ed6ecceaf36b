import { Tooltip } from 'antd';
import React from 'react';
import { EntityType } from '../../types/entity.types';
import ActionButtons from '../ActionButtons/ActionButtons';
import ReportButton from '../ReportButton/ReportButton';
import './CardView.css';

// Base type for items, requiring an id
interface BaseItem {
  id: string | number;
  [key: string]: unknown; // Use unknown instead of any for better type safety
}

// Type for meta items displayed on the card
interface MetaItem<T extends BaseItem> {
  label: string;
  value: keyof T | ((item: T) => React.ReactNode);
  className?: string;
  hideLabel?: boolean; // Controls label visibility
}

// Type for the action buttons configuration passed to CardView
// Define only the props that CardView consumes directly
interface CardActionButtonsConfig<T extends BaseItem> {
  entityType: EntityType;
  entityNameProperty: keyof T;
  onEdit: (entity: T, event: React.MouseEvent) => void;
  onDelete: (entity: T, event: React.MouseEvent) => void;
  onShare?: (entity: T, event: React.MouseEvent) => void;
  getIsDirectlyShared?: (entity: T) => boolean;
  onReport?: (entity: T, event: React.MouseEvent) => void;
  hideReportInActions?: boolean;
  // Allow other props to be passed down to ActionButtons
  [key: string]: unknown;
}

// Props for the CardView component itself
interface CardViewProps<T extends BaseItem> {
  items: T[];
  onItemClick: (e: React.MouseEvent, itemId: T['id'] | T) => void;
  onAddClick: (e: React.MouseEvent) => void;
  addButtonText: string;
  titleProperty: keyof T;
  descriptionProperty: keyof T | ((item: T) => React.ReactNode);
  metaItems?: MetaItem<T>[];
  actionButtons?: CardActionButtonsConfig<T>; // Use the specific config type
  showDescriptionTooltip?: boolean;
}

/**
 * Reusable card component for displaying items in a card view
 */
const CardView = <T extends BaseItem>({
  items,
  onItemClick,
  onAddClick,
  addButtonText,
  titleProperty,
  descriptionProperty,
  metaItems,
  actionButtons,
  showDescriptionTooltip = false,
}: CardViewProps<T>) => {
  // Helper function to get description text
  const getDescription = (item: T): React.ReactNode => {
    if (typeof descriptionProperty === 'function') {
      return descriptionProperty(item);
    }
    return item[descriptionProperty] as React.ReactNode;
  };

  // Helper function to render a single meta item (used below)
  const renderSingleMetaItem = (item: T, meta: MetaItem<T>) => {
    let value: React.ReactNode;
    if (typeof meta.value === 'function') {
      value = meta.value(item);
    } else {
      // meta.value is keyof T, safe to access
      value = item[meta.value] as React.ReactNode;
    }
    return (
      // Key added in the map function where this is called
      <span className={meta.className || 'meta-item'}>
        {!meta.hideLabel && `${meta.label}: `}
        {value}
      </span>
    );
  };

  return (
    <div className="items-container card-view">
      {items?.map((item: T) => {
        const fullDescriptionNode = getDescription(item);
        const fullDescription = React.isValidElement(fullDescriptionNode)
          ? ''
          : String(fullDescriptionNode);
        const truncatedDescription =
          fullDescription.length > 37 ? `${fullDescription.slice(0, 37)}...` : fullDescription;
        const displayDescription =
          fullDescription.length > 37 ? truncatedDescription : fullDescriptionNode;

        // Separate props for ActionButtons from the rest of the config
        const {
          entityType,
          entityNameProperty,
          onEdit,
          onDelete,
          onShare,
          getIsDirectlyShared,
          onReport,
          hideReportInActions,
          ...restActionProps
        } = actionButtons || {};

        // Separate meta items
        const lastExecutedMeta = metaItems?.find((meta) => meta.className === 'meta-date');
        const otherMetaItems = metaItems?.filter((meta) => meta.className !== 'meta-date');

        return (
          <div
            key={item.id}
            className="item card-item"
            onClick={(e: React.MouseEvent) => onItemClick(e, item.id || item)}
          >
            {/* Render Report Button at top right if onReport is provided */}
            {actionButtons && actionButtons.onReport && (
              <div className="top-right-report-button">
                <ReportButton
                  entity={item}
                  entityType={actionButtons.entityType}
                  entityNameProperty={String(item[actionButtons.entityNameProperty])}
                  onReport={actionButtons.onReport}
                />
              </div>
            )}
            <div className="item-content">
              <h3 className="item-title">{item[titleProperty] as React.ReactNode}</h3>
              <p className="item-description">
                <Tooltip
                  title={
                    showDescriptionTooltip && fullDescription.length > 37
                      ? fullDescription
                      : undefined
                  }
                >
                  <span>{displayDescription}</span>
                </Tooltip>
              </p>
              <div className="item-meta">
                {/* Render other meta items first */}
                {otherMetaItems?.map((meta, index) => (
                  <React.Fragment key={`meta-${index}`}>
                    {renderSingleMetaItem(item, meta)}
                  </React.Fragment>
                ))}

                {/* Render the row for last executed date and actions */}
                {(lastExecutedMeta || actionButtons) && (
                  <div className="item-meta-row">
                    {/* Render Last Executed meta item */}
                    <span className="meta-item-label">
                      {' '}
                      {/* Optional wrapper if needed for styling */}
                      {lastExecutedMeta && renderSingleMetaItem(item, lastExecutedMeta)}
                    </span>

                    {/* Render Action Buttons */}
                    {actionButtons && entityType && entityNameProperty && onEdit && onDelete && (
                      <div className="item-actions">
                        <ActionButtons
                          entity={item}
                          entityType={entityType}
                          entityNameProperty={String(item[entityNameProperty])}
                          onEdit={onEdit}
                          onDelete={onDelete}
                          onShare={onShare}
                          isDirectlyShared={getIsDirectlyShared ? getIsDirectlyShared(item) : true}
                          onReport={onReport}
                          hideReportInActions={hideReportInActions}
                          {...restActionProps}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
      })}
      <div className="add-card" onClick={onAddClick}>
        <div className="add-card-content">
          <i className="fas fa-plus"></i>
          <p>{addButtonText}</p>
        </div>
      </div>
    </div>
  );
};

export default CardView;
