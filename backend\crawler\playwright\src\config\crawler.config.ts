export const CRAWLER_CONFIG = {
  pool: {
    maxConcurrency: parseInt(process.env.MAX_CONCURRENT_CRAWLS || "10", 10),
    requestTimeout: parseInt(process.env.CRAWLER_REQUEST_TIMEOUT || "120000", 10), // 2 minutes
    maxQueueSize: parseInt(process.env.CRAWLER_QUEUE_SIZE || "100", 10),
    headless: process.env.CRAWLER_HEADLESS !== "false",
  },
  browser: {
    args: [
      "--ignore-certificate-errors",
      "--disable-extensions",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--no-first-run",
      "--no-zygote",
      "--disable-gpu",
      "--disable-infobars",
      "--disable-notifications",
      "--disable-backgrounding-occluded-windows",
    ]
  }
};