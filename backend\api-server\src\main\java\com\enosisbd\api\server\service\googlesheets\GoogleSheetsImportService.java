package com.enosisbd.api.server.service.googlesheets;

import com.enosisbd.api.server.dto.GoogleSheetImportRequestDto;
import com.enosisbd.api.server.dto.GoogleSheetImportResponseDto;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.security.GeneralSecurityException;

/**
 * Service for importing data from Google Sheets
 */
public interface GoogleSheetsImportService {

    /**
     * Import data from a Google Sheet and create a project with modules and submodules
     *
     * @param request The import request containing the sheet URL and parameters
     * @param userEmail The email of the user making the request (for Google OAuth access)
     * @return The import response containing the created project and its tree structure
     * @throws IOException If there is an error accessing the sheet
     * @throws GeneralSecurityException If there is a security error
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    GoogleSheetImportResponseDto importFromGoogleSheet(GoogleSheetImportRequestDto request, String userEmail)
            throws IOException, GeneralSecurityException;
}
