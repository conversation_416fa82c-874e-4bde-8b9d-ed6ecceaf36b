import { jwtDecode } from 'jwt-decode';
import React, { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AuthenticationService from '../../services/authentication.service';
import UserProfileService from '../../services/user-profile.service';
import { UserPreference } from '../../types/user-profile.types';
import { AuthContext } from './AuthContextInstance';

interface User {
  id: string;
  authProvider: string;
}

interface DecodedToken {
  roles?: string[];
  sub: string;
  exp?: number;
  authProvider?: string;
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  roles: string[];
  userPreferences: UserPreference | null;
  login: (accessToken: string, refreshToken: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  isLoading: boolean;
  refreshUserPreferences: () => Promise<UserPreference>;
  updateUserPreferences: (preferences: UserPreference) => Promise<UserPreference>;
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  // Initialize state directly from localStorage
  const [token, setToken] = useState<string | null>(() => localStorage.getItem('token'));
  const [refreshToken, setRefreshToken] = useState<string | null>(() =>
    localStorage.getItem('refreshToken'),
  );
  const [roles, setRoles] = useState<string[]>([]);
  const [userPreferences, setUserPreferences] = useState<UserPreference | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const refreshTimeout = useRef<NodeJS.Timeout | null>(null);
  const isRefreshScheduled = useRef(false);
  const navigate = useNavigate();

  const clearRefreshTimeout = () => {
    if (refreshTimeout.current) {
      clearTimeout(refreshTimeout.current);
      refreshTimeout.current = null;
      isRefreshScheduled.current = false;
    }
  };

  const scheduleTokenRefresh = (exp: number) => {
    clearRefreshTimeout();

    if (isRefreshScheduled.current) {
      return;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = exp - currentTime;

    // Refresh at 80% of the token's lifetime or when there's less than 30 seconds left
    const refreshIn = Math.min(Math.max(timeUntilExpiry * 0.8, 30), timeUntilExpiry - 10);

    if (timeUntilExpiry <= 30) {
      console.debug('[Auth] Token expired or about to expire, refreshing immediately');
      refreshAuthToken().catch((error) => {
        console.error('[Auth] Immediate refresh failed:', error);
        logout();
      });
      return;
    }

    console.debug(
      `[Auth] Scheduling token refresh in ${Math.floor(refreshIn)} seconds (${Math.round(
        refreshIn / 60,
      )} minutes)`,
    );

    isRefreshScheduled.current = true;
    refreshTimeout.current = setTimeout(async () => {
      isRefreshScheduled.current = false;
      try {
        console.log('[Auth] Refreshing token...');
        const newToken = await refreshAuthToken();
        const decoded: DecodedToken = jwtDecode(newToken);
        if (decoded.exp) {
          scheduleTokenRefresh(decoded.exp);
        }
      } catch (error) {
        console.error('[Auth] Scheduled refresh failed:', error);
        logout();
      }
    }, refreshIn * 1000);
  };

  const refreshUserPreferences = async (): Promise<UserPreference> => {
    try {
      const preferences = await UserProfileService.getUserPreferences();
      setUserPreferences(preferences);
      return preferences;
    } catch (error) {
      console.error('[Auth] Failed to fetch user preferences:', error);
      throw error;
    }
  };

  const updateUserPreferences = async (preferences: UserPreference): Promise<UserPreference> => {
    try {
      const updatedPreferences = await UserProfileService.updateUserPreferences(preferences);
      setUserPreferences(updatedPreferences);
      return updatedPreferences;
    } catch (error) {
      console.error('[Auth] Failed to update user preferences:', error);
      throw error;
    }
  };

  const checkAuth = async () => {
    setIsLoading(true);
    try {
      const currentToken = localStorage.getItem('token');
      if (!currentToken) {
        setUser(null);
        setRoles([]);
        setUserPreferences(null);
        setIsLoading(false);
        return;
      }

      const decoded: DecodedToken = jwtDecode(currentToken);
      const now = Math.floor(Date.now() / 1000);

      if (!decoded.exp) {
        throw new Error('Token has no expiration');
      }

      if (decoded.exp > now) {
        // Valid token
        setRoles(decoded.roles || []);
        setUser({ id: decoded.sub || '', authProvider: decoded.authProvider || '' });
        scheduleTokenRefresh(decoded.exp);

        // Fetch user preferences if not already loaded
        if (!userPreferences) {
          try {
            await refreshUserPreferences();
          } catch (error) {
            console.error('[Auth] Failed to load user preferences during auth check:', error);
          }
        }
      } else {
        // Token expired, try to refresh
        const newToken = await refreshAuthToken();
        const newDecoded: DecodedToken = jwtDecode(newToken);
        setRoles(newDecoded.roles || []);
        setUser({ id: newDecoded.sub || '', authProvider: newDecoded.authProvider || ''});
        if (newDecoded.exp) {
          scheduleTokenRefresh(newDecoded.exp);
        }

        // Refresh user preferences after token refresh
        try {
          await refreshUserPreferences();
        } catch (error) {
          console.error('[Auth] Failed to load user preferences after token refresh:', error);
        }
      }
    } catch (error) {
      console.error('[Auth] Authentication check failed:', error);
      logout();
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkAuth();
    return () => {
      clearRefreshTimeout();
    };
  }, []); // Empty dependency array is correct here

  const login = async (accessToken: string, refreshToken: string) => {
    setIsLoading(true);
    try {
      const decoded: DecodedToken = jwtDecode(accessToken);
      if (!decoded.exp) {
        throw new Error('Token has no expiration');
      }

      setRoles(decoded.roles || []);
      setUser({ id: decoded.sub || '', authProvider: decoded.authProvider || ''});
      setToken(accessToken);
      setRefreshToken(refreshToken);
      localStorage.setItem('token', accessToken);
      localStorage.setItem('refreshToken', refreshToken);
      scheduleTokenRefresh(decoded.exp);

      // Fetch user preferences after successful login
      try {
        await refreshUserPreferences();
      } catch (error) {
        console.error('[Auth] Failed to load user preferences after login:', error);
      }
    } catch (error) {
      console.error('[Auth] Login failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    clearRefreshTimeout();
    setUser(null);
    setToken(null);
    setRefreshToken(null);
    setRoles([]);
    setUserPreferences(null);
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    navigate('/login');
  };

  const refreshAuthToken = async () => {
    const currentRefreshToken = localStorage.getItem('refreshToken'); // Get the latest refresh token
    if (!currentRefreshToken) {
      console.error('[Auth] No refresh token found in localStorage');
      throw new Error('No refresh token available');
    }

    try {
      const { accessToken: newToken, refreshToken: newRefreshToken } =
        await AuthenticationService.refreshToken(currentRefreshToken);

      if (!newToken || !newRefreshToken) {
        throw new Error('Invalid response from refresh token endpoint');
      }

      // Update both tokens
      setToken(newToken);
      setRefreshToken(newRefreshToken);
      localStorage.setItem('token', newToken);
      localStorage.setItem('refreshToken', newRefreshToken);

      // Update user and roles from new token
      const decoded: DecodedToken = jwtDecode(newToken);
      setRoles(decoded.roles || []);
      setUser({ id: decoded.sub || '', authProvider: decoded.authProvider || ''});

      return newToken;
    } catch (error) {
      console.error('[Auth] Refresh token failed:', error);
      logout(); // Only logout if refresh actually failed
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    token,
    refreshToken,
    roles,
    userPreferences,
    login,
    logout,
    isAuthenticated: !!token,
    isLoading,
    refreshUserPreferences,
    updateUserPreferences,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export { AuthContext };
