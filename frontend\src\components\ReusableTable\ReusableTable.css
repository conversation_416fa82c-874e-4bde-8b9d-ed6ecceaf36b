.sort-indicator {
  display: inline-flex;
  align-items: center;
}

.table-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.reusable-table {
  width: 100%;
  border-collapse: collapse;
}

.reusable-table th,
.reusable-table td {
  padding: 0.875rem 1.25rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.reusable-table th {
  background-color: #f8fafc;
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.8125rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-header {
  position: sticky;
  top: 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0.5rem;
  background-color: #f8fafc;
  border-bottom: 1px solid var(--border-color);
}

/* For sortable headers */
.reusable-table th.sortable-header {
  cursor: pointer;
}

.reusable-table th.sortable-header:hover {
  background-color: #f1f5f9;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.reusable-table tbody tr {
  transition: background-color 0.2s;
}

.reusable-table tbody tr:hover {
  background-color: #f1f5f9;
  cursor: pointer;
}

.reusable-table td.title-cell,
.reusable-table .title-cell {
  font-weight: 500;
  color: var(--primary-text);
}

.reusable-table td.description-cell,
.reusable-table .description-cell {
  max-width: 400px;
  color: var(--text-secondary);
  font-size: var(--font-regular);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reusable-table .regular-cell {
  color: var(--text-secondary);
  font-size: var(--font-regular);
  white-space: nowrap;
}

.reusable-table .actions-cell {
  width: 100px;
}

.reusable-table .table-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  padding: 0.5rem;
  background-color: #f8fafc;
  border-top: 1px solid var(--border-color);
}

/* Style adjustments for Ant Design pagination */
.table-footer .ant-pagination {
  margin: 0;
}

.table-footer .ant-pagination .ant-pagination-item,
.table-footer .ant-pagination .ant-pagination-prev,
.table-footer .ant-pagination .ant-pagination-next {
  height: 30px !important;
  min-width: 30px !important;
}

.table-footer .ant-pagination .ant-pagination-options .ant-select-selector {
  height: 30px !important;
}

.table-footer .ant-select-selector {
  border-radius: 4px !important;
}

.table-footer .ant-pagination-options-size-changer.ant-select {
  margin-right: 0;
}
