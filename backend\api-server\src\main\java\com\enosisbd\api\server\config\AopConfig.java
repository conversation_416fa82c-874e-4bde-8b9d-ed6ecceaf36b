package com.enosisbd.api.server.config;

import org.springframework.aop.interceptor.ExposeInvocationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.Ordered;

@Configuration
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class AopConfig {

    /**
     * Register the ExposeInvocationInterceptor with the highest precedence
     * to ensure it runs before any other aspect.
     */
    @Bean
    public ExposeInvocationInterceptor exposeInvocationInterceptor() {
        return ExposeInvocationInterceptor.INSTANCE;
    }
}
