# Google Sheets API Integration

This document provides instructions for setting up the Google Sheets API integration for the Enosis Portal application.

## Prerequisites

1. A Google Cloud Platform (GCP) account
2. A GCP project with the Google Sheets API and Drive API enabled
3. A service account with access to the Google Sheets API and Drive API

## Setup Instructions

### 1. Create a Google Cloud Platform Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Make note of your project ID

### 2. Enable the Google Sheets API and Drive API

1. In the Google Cloud Console, navigate to "APIs & Services" > "Library"
2. Search for "Google Sheets API"
3. Click on the API and click "Enable"
4. Search for "Google Drive API"
5. Click on the API and click "Enable"

### 3. Create a Service Account

1. In the Google Cloud Console, navigate to "APIs & Services" > "Credentials"
2. Click "Create Credentials" and select "Service Account"
3. Enter a name for your service account and click "Create"
4. Assign the role "Project" > "Viewer" to the service account
5. Click "Done"

### 4. Create a Service Account Key

1. In the Google Cloud Console, navigate to "APIs & Services" > "Credentials"
2. Find your service account in the list and click on it
3. Click on the "Keys" tab
4. Click "Add Key" and select "Create new key"
5. Select "JSON" as the key type and click "Create"
6. The key file will be downloaded to your computer

### 5. Configure the Application

#### Service Account Credentials

There are two approaches for storing service account credentials, each with its own advantages:

##### Option 1: Using a credentials.json file (Recommended for Development)

1. Rename the downloaded key file to `credentials.json`
2. Place the file in the `src/main/resources` directory of the application
3. Alternatively, set the `GOOGLE_SHEETS_CREDENTIALS_FILE` environment variable to the path of your credentials file

**Advantages:**

- Easier to set up and test locally
- Contains all necessary information in one file
- Standard approach recommended by Google

**Disadvantages:**

- Not ideal for production as it requires storing a sensitive file

##### Option 2: Using Environment Variables (Recommended for Production)

Instead of using a credentials file, you can extract the key information and store it in environment variables:

1. Set the following environment variables:
   - `GOOGLE_CLIENT_ID`: The client ID from the service account
   - `GOOGLE_CLIENT_SECRET`: The client secret
   - `GOOGLE_PROJECT_ID`: The Google Cloud project ID
   - `GOOGLE_PRIVATE_KEY`: The private key (be careful with newlines)

**Advantages:**

- More secure for production environments
- Follows the 12-factor app methodology
- No sensitive files to manage

**Disadvantages:**

- More complex to set up
- Requires careful handling of the private key format

#### Additional Configuration

- Set the `GOOGLE_SHEETS_USE_SERVICE_ACCOUNT` environment variable to `false` if you want to use user-specific OAuth credentials instead of the service account
- Set the `GOOGLE_SHEETS_REUSE_OAUTH_CONFIG` environment variable to `true` to reuse the existing Google OAuth configuration

## Access Control

The application supports two modes of accessing Google Sheets:

### 1. Service Account Access (Default)

For the application to access a Google Sheet using the service account, the sheet must be shared with the service account email address. The email address can be found in the `client_email` field of the credentials.json file.

1. Open the Google Sheet you want to access
2. Click the "Share" button
3. Enter the service account email address
4. Select "Viewer" as the permission
5. Click "Send"

### 2. User-Specific Access (OAuth)

If a user logs in using Google OAuth, the application can access Google Sheets that the user has access to without sharing them with the service account. This requires:

1. Users to log in using Google OAuth
2. The application to be configured to use user-specific credentials (`GOOGLE_SHEETS_USE_SERVICE_ACCOUNT=false`)
3. The Google OAuth scope to include access to Google Sheets and Drive

## Using the API

The API endpoint for importing Google Sheets data is:

```
POST /api/google-sheets/import
```

Request body:

```json
{
  "sheetUrl": "https://docs.google.com/spreadsheets/d/your-sheet-id/edit",
  "submoduleColumn": "B",
  "submoduleStartRow": 9,
  "projectName": "Optional Project Name"
}
```

If `projectName` is not provided, the name of the first sheet will be used as the project name.

The API automatically detects if the user is logged in with Google OAuth and uses their email for access control if available.

## Troubleshooting

- If you get a 403 error, make sure the Google Sheet is shared with the service account email address or the user has access to it if using OAuth
- If you get a 404 error, make sure the Google Sheet URL is correct
- If you get a 500 error, check the application logs for more details
- If you're using OAuth and still getting access errors, make sure the OAuth scopes include access to Google Sheets and Drive
