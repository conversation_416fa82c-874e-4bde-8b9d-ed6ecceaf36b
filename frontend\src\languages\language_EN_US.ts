const language_EN_US = {
  TESTCASE_PASSED: 'PASSED',
  TESTCASE_FAILED: 'FAILED',
  NEXT_BUTTON: 'Next',
  CANCEL_BUTTON: 'Cancel',
  SAVE_BUTTON: 'Save',
  ACCEPT_BUTTON: 'Accept',
  VERSION_MODAL: {
    TITLE: 'Create Version',
    VERSION_NAME_LABEL: 'Version Name',
    TESTABLE_TEST_SUITES_LABEL: 'Testable Test Suites',
    SOURCE_VERSION_LABEL: 'Source Version',
    TEST_CASE_GENERATION_METHOD_LABEL: 'Test Case Generation Method',
    GENERATE_AUTOMATION_SCRIPT_LABEL: 'Generate Automation Script',
    PLATFORM_LABEL: 'Platform',
    LANGUAGE_LABEL: 'Language',
    CREATE_VERSION: 'Create Version',
    UPDATE_VERSION: 'Update Version',
    SELECT_VERSION_TO_CLONE: 'Select a version to clone',
    TEST_CASES_LABEL: 'Test Cases',
    GENERATE_TEST_CASES_THROUGH_AI: 'Generate Test Cases through AI',
    MANUALLY_INPUT_TEST_CASES: 'Manually Input Test Cases',
    AUTOMATION_SCRIPT_LABEL: 'Test Automation Script',
    GENERATE_AUTOMATION_SCRIPT_THROUGH_AI_CHECKBOX: 'Generate through AI',
    TESTABLE_TEST_SUITES_PLACEHOLDER: 'Enter test suite names, comma-separated',
  },
  AUTOMATE_PROCESS: {
    TESTCASES: {
      GENERATE_TEST_CASES_BUTTON: 'Generate Test Cases',
    },
  },
  USER_PROFILE: {
    TITLE: 'User Profile',
    PROFILE_INFO: 'Profile Information',
    FULL_NAME: 'Full Name',
    FULL_NAME_PLACEHOLDER: 'Enter your full name',
    EMAIL: 'Email Address',
    EMAIL_MESSAGE: "Email address cannot be changed",
    PREFERENCES: 'User Preferences',
    DEFAULT_VIEW_MODE: 'Default View Mode',
    DEFAULT_VIEW_MODE_DESC: 'Set your preferred default view mode.',
    CARD_VIEW: 'Card View',
    LIST_VIEW: 'List View',
    SAVE_PREFERENCES: 'Save Preferences',
    SAVE_CHANGES: 'Save Changes',
    CHANGE_PASSWORD: 'Change Password',
    CHANGING_PASSWORD: 'Changing Password...',
    CURRENT_PASSWORD: 'Current Password',
    CURRENT_PASSWORD_PLACEHOLDER: 'Enter your current password',
    NEW_PASSWORD: 'New Password',
    NEW_PASSWORD_PLACEHOLDER: 'Enter your new password',
    CONFIRM_NEW_PASSWORD: 'Confirm New Password',
    CONFIRM_NEW_PASSWORD_PLACEHOLDER: 'Confirm your new password',
    PASSWORD_CHANGE_FAILED: 'Failed to change password',
    PASSWORD_CHANGE_SUCCESS: 'Password changed successfully',
    PASSWORD_VALIDATION: 'Password must contain: 8+ characters, 1 uppercase, 1 lowercase, 1 number, and 1 special character',
    PASSWORD_VALIDATION_MATCH: 'New password and confirm password do not match',
    PREFERENCES_SAVED_SUCCESS: 'Preferences saved successfully',
    PREFERENCES_SAVED_ERROR: 'Error saving preferences',
    PROFILE_UPDATE_SUCCESS: 'Profile updated successfully',
    PROFILE_UPDATE_ERROR: 'Failed to update profile',
    FULLNAME_VALIDATION_MIN_LENGTH: 'Full name must be at least 4 characters',
    FULLNAME_VALIDATION_MAX_LENGTH: 'Full name cannot exceed 255 characters',
    FETCH_ERROR: 'Error fetching user data',
    GOOGLE_AUTH_PASSWORD_MESSAGE: "Password change is not available for accounts using Google authentication. Please manage your password through your Google account settings.",
  },
};

export default language_EN_US;
