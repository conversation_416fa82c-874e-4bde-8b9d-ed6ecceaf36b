import { LLM_TEST_CASE_GENERATION_URL, LLM_TEST_SCRIPT_GENERATION_URL } from '../../config';
import { LLMRequestParams } from '../types/llm.types';
import { prepareLLMRequest } from '../utils/common.utils';
import httpService from './http.service';

/// merge below two methods into one
export const LLmService = {
  generateTestCases: async (
    requirement: string,
    onChunk: (chunk: string) => void,
  ): Promise<{ stream: string }> => {
    return generateLLMResponse(requirement, LLM_TEST_CASE_GENERATION_URL, onChunk);
  },

  generateCode: async (
    promptData: string,
    systemPrompt: string,
    onChunk: (chunk: string) => void,
  ): Promise<{ stream: string }> => {
    const requestParams = {
      data: promptData,
      systemCommand: systemPrompt, // Extract system prompt from the request
    };
    return generateLLMResponse(requestParams, LLM_TEST_SCRIPT_GENERATION_URL, onChunk);
  },
};

const generateLLMResponse = async (
  data: string | LLMRequestParams,
  url: string,
  onChunk: (chunk: string) => void,
): Promise<{ stream: string }> => {
  await httpService.stream(url, prepareLLMRequest(data), onChunk);
  const stream = 'Generated successfully';
  return {
    stream,
  };
};
