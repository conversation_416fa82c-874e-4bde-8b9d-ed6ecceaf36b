import { EventEmitter } from "events";
import { Browser, chromium } from "playwright";
import { CRAWLER_CONFIG } from "../config/crawler.config";
import {
  AuthConfig,
  CrawlOptions,
  CrawlResult,
} from "../types/web-crawler.types";
import logger from "../utils/logger";

interface CrawlRequest {
  id: string;
  url: string;
  pageName: string;
  authConfig?: AuthConfig;
  options?: CrawlOptions;
  resolve: (result: CrawlResult) => void;
  reject: (error: Error) => void;
  startTime: number;
  timeout?: NodeJS.Timeout;
}

interface CrawlerPoolOptions {
  maxConcurrency?: number;
  requestTimeout?: number;
  maxQueueSize?: number;
  headless?: boolean;
}

const DEFAULT_POOL_OPTIONS: Required<CrawlerPoolOptions> = CRAWLER_CONFIG.pool;

export class WebCrawlerPool extends EventEmitter {
  private browsers: Browser[] = [];
  private crawlerAvailable: boolean[] = [];
  private requestQueue: CrawlRequest[] = [];
  private activeRequests: Map<string, CrawlRequest> = new Map();
  private options: Required<CrawlerPoolOptions>;
  private isInitialized = false;
  private isShuttingDown = false;

  constructor(options: CrawlerPoolOptions = {}) {
    super();
    this.options = { ...DEFAULT_POOL_OPTIONS, ...options };

    this.crawlerAvailable = Array(this.options.maxConcurrency).fill(true);

    process.on("SIGTERM", this.shutdown.bind(this));
    process.on("SIGINT", this.shutdown.bind(this));

    logger.info(
      `WebCrawlerPool initialized with ${this.options.maxConcurrency} concurrent crawlers`
    );
  }

  private async initPoolIfNeeded(): Promise<void> {
    if (this.isInitialized || this.isShuttingDown) return;

    try {
      logger.info(
        `Initializing crawler pool with ${this.options.maxConcurrency} browsers`
      );

      // Launch browsers in parallel
      const browserPromises = Array(this.options.maxConcurrency)
        .fill(null)
        .map(() => this.launchBrowser());

      this.browsers = await Promise.all(browserPromises);
      this.isInitialized = true;

      logger.info("Crawler pool initialization complete");
    } catch (error) {
      logger.error("Failed to initialize browser pool:", error);
      throw new Error("Browser pool initialization failed");
    }
  }

  private async launchBrowser(): Promise<Browser> {
    return chromium.launch({
      headless: this.options.headless,
      args: CRAWLER_CONFIG.browser.args,
    });
  }

  async crawl(
    url: string,
    pageName: string,
    authConfig?: AuthConfig,
    crawlOptions?: CrawlOptions
  ): Promise<CrawlResult> {
    if (this.isShuttingDown) {
      throw new Error("Service is shutting down, no new requests accepted");
    }

    // Initialize pool lazily on first request
    if (!this.isInitialized) {
      await this.initPoolIfNeeded();
    }

    // Generate unique request ID
    const requestId = `req_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 9)}`;

    return new Promise<CrawlResult>((resolve, reject) => {
      if (this.requestQueue.length >= this.options.maxQueueSize) {
        return reject(new Error("Request queue is full, try again later"));
      }

      const request: CrawlRequest = {
        id: requestId,
        url,
        pageName,
        authConfig,
        options: crawlOptions,
        resolve,
        reject,
        startTime: Date.now(),
      };

      request.timeout = setTimeout(() => {
        this.handleRequestTimeout(requestId);
      }, this.options.requestTimeout);

      this.requestQueue.push(request);
      this.processQueue();

      logger.debug(`Request ${requestId} queued for URL: ${url}`);
    });
  }

  private async processQueue(): Promise<void> {
    if (this.requestQueue.length === 0 || this.isShuttingDown) return;
    const availableCrawlerIndex = this.crawlerAvailable.findIndex(
      (available) => available
    );

    if (availableCrawlerIndex === -1) {
      return;
    }

    // Mark crawler as busy
    this.crawlerAvailable[availableCrawlerIndex] = false;

    // Get next request
    const request = this.requestQueue.shift();
    if (!request) {
      // This should never happen, but just in case
      this.crawlerAvailable[availableCrawlerIndex] = true;
      return;
    }

    // Track active request
    this.activeRequests.set(request.id, request);

    logger.info(
      `Processing request ${request.id} for URL: ${request.url} (waited ${
        Date.now() - request.startTime
      }ms)`
    );

    try {
      const result = await this.executeCrawl(
        availableCrawlerIndex,
        request.url,
        request.pageName,
        request.authConfig,
        request.options
      );

      // Clear timeout and resolve promise
      if (request.timeout) clearTimeout(request.timeout);
      request.resolve(result);
    } catch (error) {
      if (request.timeout) clearTimeout(request.timeout);
      request.reject(error instanceof Error ? error : new Error(String(error)));
    } finally {
      this.activeRequests.delete(request.id);
      this.crawlerAvailable[availableCrawlerIndex] = true;

      // Process next request if any
      this.processQueue();
    }
  }

  private async executeCrawl(
    browserIndex: number,
    url: string,
    pageName: string,
    authConfig?: AuthConfig,
    crawlOptions?: CrawlOptions
  ): Promise<CrawlResult> {
    const browser = this.browsers[browserIndex];

    const { WebCrawler } = await import("./web-crawler.service");

    const tempCrawler = new WebCrawler({
      browser, // Pass existing browser instance
      headless: this.options.headless,
    });

    try {
      return await tempCrawler.crawl(url, pageName, authConfig, crawlOptions);
    } finally {
      await tempCrawler.releaseResources();
    }
  }

  private handleRequestTimeout(requestId: string): void {
    const request = this.activeRequests.get(requestId);

    if (!request) {
      // Request might be in queue, find and remove it
      const queueIndex = this.requestQueue.findIndex(
        (req) => req.id === requestId
      );
      if (queueIndex !== -1) {
        const timedOutRequest = this.requestQueue.splice(queueIndex, 1)[0];
        timedOutRequest.reject(
          new Error(`Request timed out after ${this.options.requestTimeout}ms`)
        );
      }
      return;
    }

    logger.warn(
      `Request ${requestId} timed out after ${this.options.requestTimeout}ms`
    );
    this.activeRequests.delete(requestId);
    request.reject(
      new Error(`Request timed out after ${this.options.requestTimeout}ms`)
    );
  }

  getQueueStatus(): {
    activeRequests: number;
    queuedRequests: number;
    availableCrawlers: number;
  } {
    return {
      activeRequests: this.activeRequests.size,
      queuedRequests: this.requestQueue.length,
      availableCrawlers: this.crawlerAvailable.filter(Boolean).length,
    };
  }

  async shutdown(): Promise<void> {
    if (this.isShuttingDown) return;

    this.isShuttingDown = true;
    logger.info("Shutting down WebCrawlerPool...");

    // Reject all queued requests
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (request) {
        if (request.timeout) clearTimeout(request.timeout);
        request.reject(new Error("Service is shutting down"));
      }
    }

    // Reject all active requests
    for (const request of this.activeRequests.values()) {
      if (request.timeout) clearTimeout(request.timeout);
      request.reject(new Error("Service is shutting down"));
    }

    this.activeRequests.clear();

    // Close all browsers
    const closingPromises = this.browsers.map((browser) =>
      browser
        .close()
        .catch((err) =>
          logger.error("Error closing browser during shutdown:", err)
        )
    );

    await Promise.all(closingPromises);
    this.browsers = [];
    this.isInitialized = false;

    logger.info("WebCrawlerPool shutdown complete");
  }
}
