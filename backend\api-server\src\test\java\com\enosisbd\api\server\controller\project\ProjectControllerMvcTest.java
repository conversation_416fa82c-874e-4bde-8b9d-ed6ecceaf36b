package com.enosisbd.api.server.controller.project;

import com.enosisbd.api.server.dto.TreeNodeDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.service.crawledPage.CrawledPageService;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.project.ProjectService;
import com.enosisbd.api.server.service.tree.ProjectTreeService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.Collections;

import static org.hamcrest.Matchers.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ProjectController.class)
public class ProjectControllerMvcTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ProjectService projectService;

    @MockBean
    private ModuleService moduleService;

    @MockBean
    private CrawledPageService crawledPageService;

    @MockBean
    private ProjectTreeService projectTreeService;

    @Test
    void getProjectTree_ShouldReturnTreeStructure() throws Exception {
        // Arrange
        Long projectId = 1L;
        
        // Create a simple tree structure
        TreeNodeDto moduleNode = TreeNodeDto.builder()
                .id(1L)
                .name("Module 1")
                .type(EntityType.MODULE)
                .children(new ArrayList<>())
                .build();
        
        TreeNodeDto subModuleNode = TreeNodeDto.builder()
                .id(1L)
                .name("SubModule 1")
                .type(EntityType.SUBMODULE)
                .children(Collections.emptyList())
                .build();
        
        moduleNode.getChildren().add(subModuleNode);
        
        TreeNodeDto rootNode = TreeNodeDto.builder()
                .id(projectId)
                .name("Test Project")
                .type(EntityType.PROJECT)
                .children(new ArrayList<>())
                .build();
        
        rootNode.getChildren().add(moduleNode);
        
        // Mock service call
        when(projectTreeService.getProjectTree(projectId)).thenReturn(rootNode);
        
        // Act & Assert
        mockMvc.perform(get("/api/projects/{id}/tree", projectId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success", is(true)))
                .andExpect(jsonPath("$.data", notNullValue()))
                .andExpect(jsonPath("$.data.id", is(projectId.intValue())))
                .andExpect(jsonPath("$.data.name", is("Test Project")))
                .andExpect(jsonPath("$.data.type", is("PROJECT")))
                .andExpect(jsonPath("$.data.children", hasSize(1)))
                .andExpect(jsonPath("$.data.children[0].id", is(moduleNode.getId().intValue())))
                .andExpect(jsonPath("$.data.children[0].name", is("Module 1")))
                .andExpect(jsonPath("$.data.children[0].type", is("MODULE")))
                .andExpect(jsonPath("$.data.children[0].children", hasSize(1)))
                .andExpect(jsonPath("$.data.children[0].children[0].id", is(subModuleNode.getId().intValue())))
                .andExpect(jsonPath("$.data.children[0].children[0].name", is("SubModule 1")))
                .andExpect(jsonPath("$.data.children[0].children[0].type", is("SUBMODULE")))
                .andExpect(jsonPath("$.data.children[0].children[0].children", hasSize(0)));
        
        // Verify service call
        verify(projectTreeService).getProjectTree(projectId);
    }

    @Test
    void getProjectTree_ShouldReturnNotFoundWhenProjectDoesNotExist() throws Exception {
        // Arrange
        Long projectId = 999L;
        String errorMessage = "Project not found with ID: " + projectId;
        
        when(projectTreeService.getProjectTree(projectId))
                .thenThrow(new NotFoundRestException(errorMessage));
        
        // Act & Assert
        mockMvc.perform(get("/api/projects/{id}/tree", projectId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success", is(false)))
                .andExpect(jsonPath("$.message", is(errorMessage)));
        
        verify(projectTreeService).getProjectTree(projectId);
    }
}
