// auth.models.ts

// SignUp Request - Request body for user registration
export interface SignUpRequest {
    username: string;
    password: string;
  }
  
  // SignUp Response - Response after successful registration
  export interface SignUpResponse {
    userId: string;
    message: string;
  }
  
  // SignIn Request - Request body for user sign-in
  export interface SignInRequest {
    username: string;
    password: string;
  }
  
  // SignIn Response - Response after successful sign-in
  export interface SignInResponse {
    accessToken: string;
    refreshToken: string;
    tokenType: string;
  }
  
  // ResetPassword Request - Request body for resetting the password
  export interface ResetPasswordRequest {
    passwordResetToken: string;
    newPassword: string;
  }
  
  // ResetPassword Response - Response after password reset
  export interface ResetPasswordResponse {
    success: boolean;
    message: string;
  }
  
  // ForgotPassword Request - Request body for initiating password reset via email
  export interface ForgotPasswordRequest {
    email: string;
  }
  
  // ForgotPassword Response - Response after initiating the forgot password process
  export interface ForgotPasswordResponse {
    success: boolean;
    message: string;
  }
  
  // RefreshToken Request - Request body for refreshing the authentication token
  export interface RefreshTokenRequest {
    refreshToken: string;
  }
  
  // RefreshToken Response - Response containing the new access token after refresh
  export interface RefreshTokenResponse {
    accessToken: string;
  }
  
  // AuthToken - Decoded authentication token data
  export interface AuthToken {
    accessToken: string;
    refreshToken: string;
    tokenType: string;
  }
  
  // ErrorResponse - General structure for error response
  export interface ErrorResponse {
    status: number;
    message: string;
  }
  