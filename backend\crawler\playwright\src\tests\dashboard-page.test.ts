import { expect } from 'chai';
import { <PERSON><PERSON><PERSON>, Builder, By, until, WebDriver } from 'selenium-webdriver';

const CONSTANTS = {
    urls: {
        login: 'http://localhost:3000/login',
        dashboard: 'http://localhost:3000/dashboard'
    },
    selectors: {
        username: '#username',
        password: '#password',
        loginButton: '#login-btn',
        statsGrid: '.stats-grid',
        userName: '.user-name',
        logoutButton: '#logout-btn'
    },
    credentials: {
        valid: {
            username: 'admin',
            password: 'admin',
            name: 'Admin User'
        }
    }
}

describe('Dashboard Page Test', function () {
    let driver: WebDriver;

    beforeEach(async function () {
        driver = await new Builder()
            .forBrowser(Browser.CHROME)
            .usingServer("http://localhost:7070")
            .build();
    });

    afterEach(async function () {
        await driver.quit();
    });

    async function login(username: string, password: string) {
        await driver.get(CONSTANTS.urls.login);
        const usernameInput = await driver.findElement(By.css(CONSTANTS.selectors.username));
        await usernameInput.sendKeys(username);
        const passwordInput = await driver.findElement(By.css(CONSTANTS.selectors.password));
        await passwordInput.sendKeys(password);
        const loginButton = await driver.findElement(By.css(CONSTANTS.selectors.loginButton));
        await loginButton.click();
        await driver.wait(until.urlContains(CONSTANTS.urls.dashboard), 10000);
    }

    it('TC01 - should display user name on dashboard nav', async function () {
        await login(CONSTANTS.credentials.valid.username, CONSTANTS.credentials.valid.password);
        await driver.get(CONSTANTS.urls.dashboard);
        const userName = await driver.findElement(By.css(CONSTANTS.selectors.userName)).getText();
        expect(userName).to.equal(CONSTANTS.credentials.valid.name);
    });

    it('TC02 - should display stats-grid', async function () {
        await login(CONSTANTS.credentials.valid.username, CONSTANTS.credentials.valid.password);
        await driver.get(CONSTANTS.urls.dashboard);
        const statsGrid = await driver.findElement(By.css(CONSTANTS.selectors.statsGrid));
        expect(await statsGrid.isDisplayed()).to.be.true;
    });

    it('TC03 - should display logout button', async function () {
        await login(CONSTANTS.credentials.valid.username, CONSTANTS.credentials.valid.password);
        await driver.get(CONSTANTS.urls.dashboard);
        const logoutButton = await driver.findElement(By.css(CONSTANTS.selectors.logoutButton));
        expect(await logoutButton.isDisplayed()).to.be.true;
    });
    
    it('TC04 - should not be accessible without login', async function () {
        await driver.get(CONSTANTS.urls.dashboard);
        const url = await driver.getCurrentUrl();
        expect(url).to.equal(CONSTANTS.urls.login);
    });
});