<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Initial schema creation will be handled by Hibernate -->
    <include file="db/migrations/001_changelog.xml" />
    <include file="db/migrations/002_changelog.xml" />
    <!-- Load initial data -->
    <include file="db/data/initial_data.xml" />
    <!-- Google Sheets integration -->
    <include file="db/migrations/20240601_add_google_sheets_metadata_to_module.xml" />
    <include file="db/migrations/20240602_move_google_sheets_metadata_to_project.xml" />
    <include file="db/migrations/20240603_add_range_to_submodule.xml" />
    <include file="db/migrations/20240604_add_test_case_columns_to_project.xml" />

</databaseChangeLog>