import { Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { AuthenticationService } from '../../../services/authentication.service';
import { useAuth } from '../../ContextApi/useAuth';
import './AuthPage.css';

const OAuthCallbackPage: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();

  useEffect(() => {
    const processOAuthCallback = async () => {
      try {
        const searchParams = new URLSearchParams(location.search);
        const code = searchParams.get('code');
        const state = searchParams.get('state');

        if (!code) {
          setError('No authorization code received');
          return;
        }

        if (state === 'google') {
          const response = await AuthenticationService.handleGoogleCallback(code);
          await login(response.accessToken, response.refreshToken);
          navigate('/projects');
        }
      } catch (err: any) {
        setError(err.message || 'Authentication failed');
        console.error(err);
      }
    };

    processOAuthCallback();
  }, [location, login, navigate]);

  if (error) {
    return (
      <div className="auth-container">
        <h3 className="auth-title">Authentication Error</h3>
        <div className="alert alert-error">{error}</div>
        <button onClick={() => navigate('/login')} className="btn-primary auth-button">
          Return to Login
        </button>
      </div>
    );
  }

  return (
    <div className="oauth-callback-loading">
      <Spin size="large" />
      <p className="alert">Completing authentication, please wait...</p>
    </div>
  );
};

export default OAuthCallbackPage;
