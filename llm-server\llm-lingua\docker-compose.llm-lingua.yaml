services:
  llmlingua:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./app:/app
    environment:
      - PYTHONUNBUFFERED=1
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
    networks:
      llmlingua-network:
        ipv4_address: **************

networks:
  llmlingua-network:
    driver: bridge
    ipam:
      config:
        - subnet: *************/24
