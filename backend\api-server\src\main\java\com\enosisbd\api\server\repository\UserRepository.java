package com.enosisbd.api.server.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.enosisbd.api.server.entity.User;

import io.hypersistence.utils.spring.repository.BaseJpaRepository;

@Repository
public interface UserRepository extends BaseJpaRepository<User, Long> {
    Optional<User> findByEmailIgnoreCase(String email);

    @Query("""
        SELECT DISTINCT u FROM User u
        LEFT JOIN u.roles r2 ON r2.name = 'ADMIN'
        WHERE r2.id IS NULL
        ORDER BY u.id ASC
    """)
    List<User> findAllNonAdminUsers();

    @Query("SELECT u FROM User u WHERE u.id = :userId")
    Optional<User> findActiveById(Long userId);

    @Query("""
        SELECT DISTINCT u FROM User u
        JOIN u.roles r
        LEFT JOIN u.roles r2 ON r2.name = 'ADMIN'
        WHERE (u.fullName ILIKE CONCAT('%', :query, '%') OR u.email ILIKE CONCAT(:query, '%'))
        AND u.approved = true
        AND r2.id IS NULL
        ORDER BY u.fullName ASC
    """)
    List<User> searchByNameOrEmail(@Param("query") String query);

    @Query("""
        SELECT DISTINCT u FROM User u
        JOIN u.roles r
        LEFT JOIN u.roles r2 ON r2.name = 'ADMIN'
        WHERE (u.fullName ILIKE CONCAT('%', :query, '%') OR u.email ILIKE CONCAT(:query, '%'))
        AND u.approved = true
        AND u.id != :currentUserId
        AND r2.id IS NULL
        AND NOT EXISTS (
            SELECT 1 FROM EntityAccess ea
            WHERE ea.sharedWithUser.id = u.id
                AND ea.entityType.id = :entityTypeId
                AND ea.entityId = :entityId
                AND ea.isInherited = false
        )
        ORDER BY u.fullName ASC
    """)
    List<User> searchForSharing(
        @Param("query") String query,
        @Param("currentUserId") Long currentUserId,
        @Param("entityTypeId") Long entityTypeId,
        @Param("entityId") Long entityId);
 }
