package com.enosisbd.api.server.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "sharable_entity_types",
        indexes = {
                @Index(name = "idx_sharable_entity_type_name_active", columnList = "name, is_active")
        })
@Getter
@Setter
public class SharableEntityType extends BaseEntity {

    @Column(name = "is_active")
    private boolean active = true;
    @Column(name = "name", nullable = false, unique = true)
    private String name;
    @Column(name = "description")
    private String description;
}
