.admin-approval-content {
  height: calc(100vh - 235px);
  overflow: auto;
  scrollbar-width: none;
  border-radius: 12px;
  position: relative;
  flex-shrink: 0;
  box-sizing: border-box;
  background: linear-gradient(to bottom, #ffffff, #f8fafc);
  border: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 8px 2px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 10px 15px -3px rgba(234, 42, 53, 0.05), 0 4px 6px -2px rgba(234, 42, 53, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 0 rgba(234, 42, 53, 0.05);
}

.admin-approval-content .ant-table-cell {
  padding: 12px !important;
  word-break: break-word;
}

.table-wrapper {
  overflow-x: auto;
  max-width: 100%;
}

.search-filter-container {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.approval-filter-select {
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s;
  font-family: 'Poppins';
  margin-bottom: 6px;
  background: #fff;
  height: 42px;
  width: 155px;
}

.approval-filter-select:hover {
  border: 1px solid var(--border-color-fc);
}

.approval-filter-select:focus {
  outline: none;
  border: 1px solid var(--border-color-fc);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.approval-switch {
  display: flex;
  justify-content: center;
}

.switch-container {
  display: inline-flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 6px;
  border: 1px solid var(--border-color-fc);
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.switch-divider {
  width: 1px;
  height: 60%;
  background: rgba(0, 0, 0, 0.1);
}

.approve-btn,
.deny-btn {
  border: none;
  padding: 5px 12px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  position: relative;
  background: transparent;
  color: #595959;
  min-width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.approve-btn {
  border-radius: 4px 0 0 4px;
}

.deny-btn {
  border-radius: 0 4px 4px 0;
}

.approve-btn.active {
  background: #52c41a;
  color: white;
  box-shadow: 0 2px 4px rgba(82, 196, 26, 0.3);
}

.deny-btn.active {
  background: #f5222d;
  color: white;
  box-shadow: 0 2px 4px rgba(245, 34, 45, 0.3);
}

.approve-btn:not(.active):not(:disabled):hover {
  background: rgba(82, 196, 26, 0.1);
  color: #389e0d;
}

.deny-btn:not(.active):not(:disabled):hover {
  background: rgba(245, 34, 45, 0.1);
  color: #cf1322;
}

.approve-btn:disabled,
.deny-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dual-ring-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
}

.dual-ring-spinner:after {
  content: ' ';
  display: block;
  width: 12px;
  height: 12px;
  margin: 2px;
  border-radius: 50%;
  border: 2px solid currentColor;
  border-color: currentColor transparent currentColor transparent;
  animation: dual-ring 1.2s linear infinite;
}

@keyframes dual-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Add these styles for smaller notifications */
.custom-small-notification {
  font-size: 14px !important;
  font-family: 'Poppins' !important;
}

.custom-small-notification .ant-notification-notice-message {
  font-size: 14px !important;
  font-family: 'Poppins' !important;
  margin-bottom: 0 !important;
  line-height: 1.4 !important;
}

.custom-small-notification .ant-notification-notice-close {
  top: 8px !important;
  right: 8px !important;
}

.custom-small-notification .ant-notification-notice-icon {
  font-size: 16px !important;
  margin-top: 2px !important;
}
