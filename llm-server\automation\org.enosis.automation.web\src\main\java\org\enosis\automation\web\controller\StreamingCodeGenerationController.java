package org.enosis.automation.web.controller;

import org.enosis.automation.domain.api.request.ApiRequest;
import org.enosis.automation.service.stream.StreamingCodeGenerationService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.extern.log4j.Log4j2;
import reactor.core.publisher.Flux;


@Log4j2
@RestController
@Profile("streaming")
@SecurityRequirement(name = "JWT")
@CrossOrigin(value = "${app.cors.allowed-origins:http://localhost:5173}")
@RequestMapping("/api/stream/codegeneration")
public class StreamingCodeGenerationController {

    private final ChatClient qwenChatClient;
    private final ChatClient deepSeekChatClient;
    private final ChatClient qwenCoderChatClient;
    private final StreamingCodeGenerationService streamingCodeGenerationService;

    public StreamingCodeGenerationController(@Qualifier("qwenChatClient") ChatClient qwenChatClient,
                                             @Qualifier("deepSeekChatClient") ChatClient deepSeekChatClient,
                                             @Qualifier("qwenCoderChatClient") ChatClient qwenCoderChatClient,
                                             StreamingCodeGenerationService streamingCodeGenerationService) {

        this.qwenChatClient = qwenChatClient;
        this.deepSeekChatClient = deepSeekChatClient;
        this.qwenCoderChatClient = qwenCoderChatClient;
        this.streamingCodeGenerationService = streamingCodeGenerationService;
    }


    @PostMapping(path = "qwen")
    //@PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    public Flux<String> qwen(@RequestBody ApiRequest request) {
        return streamingCodeGenerationService.generate(request, qwenChatClient);
    }

    @PostMapping(path = "qwencoder")
    //@PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    public Flux<String> qwenCoder(@RequestBody ApiRequest request) {
        return streamingCodeGenerationService.generate(request, qwenCoderChatClient);
    }

    @PostMapping(path = "deepseek")
    //@PreAuthorize("hasRole('ROLE_ADMIN') or hasRole('ROLE_USER')")
    public Flux<String> deepseek(@RequestBody ApiRequest request) {
        return streamingCodeGenerationService.generate(request, deepSeekChatClient);
    }

}
