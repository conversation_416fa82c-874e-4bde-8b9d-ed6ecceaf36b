import { User } from '../types/user.types';
import httpService from './http.service';

const USER_API = {
  LIST: '/api/users/list',
  APPROVAL: '/api/approvals',
};

export const ApprovalService = {
  /**
   * Fetch all users pending approval
   */
  getAllUsers: async (): Promise<User[]> => {
    return await httpService.get<User[]>('common', USER_API.LIST);
  },

  updateApproval: async (
    email: string,
    approve: boolean,
  ): Promise<{ success: boolean; message: string }> => {
    const requestData = { email, approve }; // Create request payload
    return await httpService.post<{ success: boolean; message: string }>(
      'common',
      USER_API.APPROVAL,
      requestData,
    );
  },
};

export default ApprovalService;
