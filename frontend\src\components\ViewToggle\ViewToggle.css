.view-toggle {
  display: flex;
  align-items: center;
  background-color: var(--background-light);
  border-radius: var(--border-radius-lg);
  padding: 4 0 4 4;
  margin-left: 1rem;
  float: right;
}

.toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-primary);
}

.toggle-btn.active {
  background-color: white;
  color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.toggle-btn svg {
  width: 18px;
  height: 18px;
}
