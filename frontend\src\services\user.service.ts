import { RestResponse } from '../types/common.types';
import { SharedUser } from '../types/sharing.types';
import { User } from '../types/user.types';
import httpService from './http.service';

const API_ENDPOINTS = {
  USERS: '/api/users',
  USERS_LIST: '/api/users/list',
  SHARING_USERS_SEARCH: '/api/sharing/users/search',
  ENTITY_SHARED_USERS: (entityType: string, entityId: number) =>
    `/api/sharing/${entityType}/${entityId}/users`,
  ENTITY_SHARE: (entityType: string, entityId: number) =>
    `/api/sharing/${entityType}/${entityId}/share`,
  ENTITY_UNSHARE: (entityType: string, entityId: number, userId: number) =>
    `/api/sharing/${entityType}/${entityId}/unshare/${userId}`,
};

export const UserService = {
  /**
   * Get a list of all non-admin users
   */
  getUsers: async (): Promise<User[]> => {
    return (await httpService.get<RestResponse<User[]>>('common', API_ENDPOINTS.USERS_LIST)).data;
  },

  /**
   * Search for users by name or email
   * @param query Search query
   * @param entityType Optional entity type to exclude users who already have access
   * @param entityId Optional entity ID to exclude users who already have access
   */
  searchUsers: async (query: string, entityType?: string, entityId?: number): Promise<User[]> => {
    const params: any = { query };
    if (entityType && entityId) {
      params.entityType = entityType;
      params.entityId = entityId;
    }
    return (
      await httpService.get<RestResponse<User[]>>(
        'common',
        API_ENDPOINTS.SHARING_USERS_SEARCH,
        params,
      )
    ).data;
  },

  /**
   * Get users who have access to an entity
   * @param entityType Entity type (e.g., 'project', 'version')
   * @param entityId Entity ID
   */
  getSharedUsers: async (entityType: string, entityId: number): Promise<SharedUser[]> => {
    return (
      await httpService.get<RestResponse<SharedUser[]>>(
        'common',
        API_ENDPOINTS.ENTITY_SHARED_USERS(entityType, entityId),
      )
    ).data;
  },

  /**
   * Share an entity with a user
   * @param entityType Entity type (e.g., 'project', 'version')
   * @param entityId Entity ID
   * @param userId User ID
   */
  shareEntity: async (entityType: string, entityId: number, userId: number): Promise<void> => {
    return httpService.post('common', API_ENDPOINTS.ENTITY_SHARE(entityType, entityId), { userId });
  },

  /**
   * Remove a user's access to an entity
   * @param entityType Entity type (e.g., 'project', 'version')
   * @param entityId Entity ID
   * @param userId User ID
   */
  removeEntityAccess: async (
    entityType: string,
    entityId: number,
    userId: number,
  ): Promise<void> => {
    return httpService.delete('common', API_ENDPOINTS.ENTITY_UNSHARE(entityType, entityId, userId));
  },
};

export default UserService;
