import { MailOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import AuthenticationService from '../../../services/authentication.service';
import './AuthPage.css';

const ForgotPasswordPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');

    if (!email.trim()) {
      setError('Please enter your email address');
      return;
    }

    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setError('Please enter a valid email address');
      return;
    }

    try {
      const response = await AuthenticationService.forgotPassword(email);
      setSuccessMessage(response.message || 'If an account exists with this email, you will receive a password reset link.');
    } catch (err) {
      setError('Error sending reset link. Please try again.');
    }
  };

  return (
    <div className="auth-container">
      <h3 className="auth-title">Forgot Password?</h3>
      <p className="auth-subtitle">
        No worries! Please provide your email registered with your account. We will send a link to
        password reset
      </p>

      {successMessage && (
        <div className="alert alert-success">
          {successMessage}
        </div>
      )}

      {error && (
        <div className="alert alert-error">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="auth-form">
        <div className="form-group">
          <label htmlFor="email">
            Email Address <span className="required">*</span>
          </label>
          <div className="input-container">
            <MailOutlined className="input-icon" />
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                setError('');
                setSuccessMessage('');
              }}
              placeholder="ex: <EMAIL>"
            />
          </div>
          {error && <div className="auth-error">{error}</div>}
        </div>

        <button type="submit" className="btn-primary auth-button">
          Send Reset Link
        </button>
      </form>

      <div className="auth-footer">
        <Link to="/login" className="auth-link">
          Back to Login
        </Link>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
