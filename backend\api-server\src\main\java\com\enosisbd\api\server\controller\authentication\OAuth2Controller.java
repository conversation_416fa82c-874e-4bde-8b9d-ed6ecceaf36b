package com.enosisbd.api.server.controller.authentication;

import com.enosisbd.api.server.dto.AuthenticationDTO.*;
import com.enosisbd.api.server.service.authentication.OAuth2Service;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Log4j2
@RestController
@Tag(name = "OAuth2 Operations")
@RequestMapping("/api/auth/oauth2")
@RequiredArgsConstructor
public class OAuth2Controller implements OAuth2Api {

    private final OAuth2Service oAuth2Service;

    //Initiate Google login from this call
    @Override
    @GetMapping("/google")
    public ResponseEntity<Void> googleLogin() {
        return oAuth2Service.initiateGoogleLogin();
    }

    //Redirect callback from Google
    @Override
    @GetMapping("/google/callback")
    public ResponseEntity<Void> googleCallback(@RequestParam(required = false) String code, 
                                              @RequestParam(required = false) String error) {
        if (error != null) {
            return oAuth2Service.handleGoogleCallback(null);
        }
        return oAuth2Service.handleGoogleCallback(code);
    }

    //Call from frontend application for accessToken
    @Override
    @GetMapping("/oauth/callback")
    public ResponseEntity<SignInResponseDTO> authCallBack(@RequestParam String code) {
        return oAuth2Service.processOAuthCallback(code);
    }
}
