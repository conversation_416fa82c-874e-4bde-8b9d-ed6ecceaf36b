package com.enosisbd.api.server.model;

/**
 * Enum representing the different types of entities in the system.
 * This provides type safety and centralized definition of entity types.
 */
public enum EntityType {
    PROJECT("PROJECT"),
    MODULE("MODULE"),
    <PERSON><PERSON><PERSON><PERSON><PERSON>LE("SUBMODULE"),
    TEST_SCRIPT("TEST_SCRIPT"),
    CRAWLED_PAGE("CRAWLED_PAGE");

    private final String name;

    EntityType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    /**
     * Get an EntityType from its string name
     * @param name The name of the entity type
     * @return The corresponding EntityType, or null if not found
     */
    public static EntityType fromString(String name) {
        if (name == null) {
            return null;
        }

        for (EntityType type : EntityType.values()) {
            if (type.name.equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }
}
