#!/bin/bash
# This script is automatically turn on and off the ollama-server
# Configuration
COMPOSE_FILE="/usr/local/ollama/docker-compose.yaml"
LOG_FILE="/usr/local/ollama/docker-compose-scheduler.log"
ON_TIME="09:00"
OFF_TIME="21:30"
WORKING_DIR="/usr/local/ollama"

start_docker_compose() {
  echo "$(date) - Starting Docker Compose..." >> "$LOG_FILE"
  cd "$WORKING_DIR" || { echo "Could not change to working directory: $WORKING_DIR" >> "$LOG_FILE"; return 1; }
  docker compose -f "$COMPOSE_FILE" up -d >> "$LOG_FILE" 2>&1
  if [ $? -eq 0 ]; then
    echo "$(date) - Docker Compose started successfully." >> "$LOG_FILE"
  else
    echo "$(date) - Failed to start Docker Compose." >> "$LOG_FILE"
  fi
}

stop_docker_compose() {
  echo "$(date) - Stopping Docker Compose..." >> "$LOG_FILE"
  cd "$WORKING_DIR" || { echo "Could not change to working directory: $WORKING_DIR" >> "$LOG_FILE"; return 1; }
  docker compose -f "$COMPOSE_FILE" down >> "$LOG_FILE" 2>&1
  if [ $? -eq 0 ]; then
    echo "$(date) - Docker Compose stopped successfully." >> "$LOG_FILE"
  else
    echo "$(date) - Failed to stop Docker Compose." >> "$LOG_FILE"
  fi
}

# Main logic
current_time=$(date +%H:%M)

if [ "$current_time" == "$ON_TIME" ]; then
  start_docker_compose
elif [ "$current_time" == "$OFF_TIME" ]; then
  stop_docker_compose
fi

exit 0
