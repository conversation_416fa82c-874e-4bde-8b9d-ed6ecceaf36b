@import url('https://fonts.googleapis.com/css2?family=Sora:wght@400;700&family=Poppins:wght@400;500;700&display=swap');

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Base color palette */
  --color-red-500: #ea2a35;
  --color-red-600: #dc2626;
  --color-red-700: #b91c1c;

  --color-blue-400: #69b1ff;
  --color-blue-500: #4096ff;

  --color-slate-200: #e2e8f0;
  --color-slate-300: #bfc2c7;
  --color-slate-400: #64748b;
  --color-slate-500: #475569;
  --color-slate-600: #1e293b;
  --color-slate-700: #202430;

  --color-gray-100: #fafafa;
  --color-gray-200: #f5f5f5;
  --color-gray-300: #f0f0f0;
  --color-gray-400: #d9d9d9;
  --color-gray-500: #bfbfbf;

  --color-green-500: #16a34a;
  --color-green-600: #156633;

  --color-beige-100: #faf0e6; /* linen color */
  --color-beige-200: #f5f5dc; /* beige color */

  --color-neutral-600: #333333;
  --color-neutral-700: #222222;

  --color-white: #ffffff;
  --color-black: #000000;
  --color-black-10: rgba(0, 0, 0, 0.1);
  --color-black-25: rgba(0, 0, 0, 0.25);

  /* Functional color variables */
  --primary-color: var(--color-red-500);
  --primary-hover: var(--color-red-700);
  --secondary-color: var(--color-slate-400);
  --secondary-hover: var(--color-slate-500);
  --primary-black: var(--color-slate-700);
  --success-color: var(--color-green-500);
  --success-hover: var(--color-green-600);
  --error-color: var(--color-red-600);
  --error-hover: var(--color-red-700);
  --background-light: var(--color-gray-50, #f8fafc);
  --text-primary: var(--color-slate-600);
  --text-secondary: var(--color-slate-500);
  --border-color: var(--color-slate-200);
  --border-color-fc: var(--color-slate-300);

  --input-border-color: var(--color-gray-400);
  --input-hover-color: var(--color-blue-400);
  --input-focus-color: var(--color-blue-500);
  --input-focus-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);

  --disabled-bg: var(--color-gray-200);
  --disabled-color: var(--color-black-25);
  --placeholder-color: var(--color-gray-500);

  --table-header-bg: var(--color-gray-100);
  --table-header-bg-linen: linen;
  --table-border-color: var(--color-gray-300);
  --table-hover-bg: var(--color-gray-100);
  --table-header-text: var(--color-neutral-600);

  /* Non-color variables */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --radius-md: 0.5rem;

  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;

  --box-shadow-default: 0 2px 8px var(--color-black-10);
  --box-shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.2);

  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;

  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.25rem;

  --icon-size-sm: 16px;
  --icon-size-md: 20px;

  --transition-default: all 0.3s;
}

.app-footer {
  flex-shrink: 0;
  background-color: var(--background-light);
  border-top: 1px solid var(--border-color);
}

#root {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

body {
  background-color: var(--background-light);
  color: var(--text-primary);
  font-family: 'Poppins', system-ui, -apple-system, sans-serif;
  line-height: 1.5;
  margin: 0;
  padding: 0;
}

h1 {
  color: var(--text-primary);
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin-top: 1rem;
  margin-bottom: 2.5rem;
  letter-spacing: -0.025em;
}

.form-group {
  margin-bottom: 1.5rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-weight: 500;
  font-size: var(--font-regular);
}

/* Add/modify these styles */
input,
select,
textarea {
  border: 1px solid var(--border-color);
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border: 1px solid var(--border-color-fc);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

button {
  font-weight: 500;
  transition: all 0.2s;
}

.error {
  color: var(--error-color);
  padding: 1rem;
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: var(--radius-md);
  margin-bottom: 1.5rem;
  font-size: var(--font-regular);
}

.result {
  background-color: white;
  padding: 1.5rem;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
}

.result h2 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: var(--background-light);
  border-radius: var(--radius-md);
}

.stats p {
  margin: 0;
  font-size: var(--font-regular);
  color: var(--text-secondary);
}

.code-container {
  position: relative;
  background: #1e1e1e;
  border-radius: var(--radius-md);
  overflow: hidden;
  max-height: calc(100vh - 250px);
  height: 600px;
}

.code-block {
  margin: 0 !important;
  padding: 1.5rem;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: var(--font-regular);
  line-height: 1.6;
  overflow: auto;
  height: 100%;
  color: #d4d4d4;
  background-color: #1e1e1e;
  white-space: pre-wrap;
  word-break: break-word;
}

.code-block::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.code-block::-webkit-scrollbar-track {
  background: #1e1e1e;
  border-radius: var(--radius-md);
}

.code-block::-webkit-scrollbar-thumb {
  background-color: #3e3e3e;
  border: 3px solid #1e1e1e;
  border-radius: 10px;
}

.code-block::-webkit-scrollbar-thumb:hover {
  background-color: #4e4e4e;
}

.auth-fields {
  display: grid;
  gap: 1rem;
}

.auth-fields input {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: var(--font-regular);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: white;
  color: var(--text-primary);
  transition: all 0.2s;
}

.auth-fields input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.add-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.625rem 1.25rem;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--font-regular);
  margin-top: 1rem;
}

.add-button:hover {
  background-color: var(--primary-hover);
}

.remove-button {
  background-color: var(--error-color);
  color: white;
  border: none;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.75rem;
}

.remove-button:hover {
  background-color: #b91c1c;
}

.logo {
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-label input[type='checkbox'] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

@keyframes button-spin {
  to {
    transform: rotate(360deg);
  }
}

.button-container {
  display: flex;
  justify-content: flex-end;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.test-case-view h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 600;
}

.test-case-view ul {
  list-style-position: inside;
  margin-bottom: 1rem;
}

.test-case-view li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

@keyframes loader-spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  background-color: #fee2e2;
  color: var(--error-color);
  padding: 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 1rem;
  font-weight: 500;
}

.code-block {
  margin: 0 !important;
  padding: 1rem !important;
  font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: var(--font-regular);
  line-height: 1.5;
  overflow: auto;
  height: 100%;
  background: transparent !important;
}

.code-line {
  display: flex;
  min-width: -webkit-fill-available;
  padding: 0;
  line-height: 1.5;
}

.line-number {
  display: inline-block;
  padding: 0 1rem;
  min-width: 2.5rem;
  text-align: right;
  color: #4b5363;
  -webkit-user-select: none;
  user-select: none;
  opacity: 0.5;
}

.line-content {
  padding-right: 1.5rem;
  flex: 1;
}

.code-block::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.code-block::-webkit-scrollbar-track {
  background: #011627;
}

.code-block::-webkit-scrollbar-thumb {
  background: #1d3b53;
  border-radius: 4px;
}

.code-block::-webkit-scrollbar-thumb:hover {
  background: #2c5282;
}

.dashboard-header {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  padding: 1rem 0;
}

.dashboardHeaderPadding {
  margin-bottom: 0.3rem;
  font-weight: 600;
}

.dashboardHeaderNoPadding {
  margin-bottom: 0rem;
  font-weight: 600;
}

.dashboard-actions {
  margin-bottom: 0.3rem;
}

.dashboard-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1.5rem;
  background: linear-gradient(to bottom, #ffffff, #f8fafc);
  border-radius: 12px;
  box-shadow: 0 8px 8px 2px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 10px 15px -3px rgba(234, 42, 53, 0.05), 0 4px 6px -2px rgba(234, 42, 53, 0.03),
    0 0 0 1px rgba(255, 255, 255, 0.1), 0 1px 0 0 rgba(234, 42, 53, 0.05) inset;
  border: 1px solid rgba(226, 232, 240, 0.8);
  position: relative;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.dashboard-content::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 12px;
  padding: 1px;
  background: linear-gradient(135deg, rgba(234, 42, 53, 0.12), rgba(32, 36, 48, 0.12));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto !important;
  color: var(--text-secondary);
  font-size: var(--font-regular);
}

.search-container {
  margin-bottom: 0.3rem;
  flex: 1;
  position: relative;
  padding-left: 15px;
}

.search-input {
  width: 100%;
  padding: 0.625rem 1rem 0.625rem 2.5rem;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  font-size: var(--font-regular);
}

.search-icon {
  position: absolute;
  left: 1.875rem;
  top: 56%;
  transform: translateY(-50%);
  color: var(--text-secondary);
}

.error-message {
  background-color: #fee2e2;
  border-left: 4px solid #ef4444;
  color: #b91c1c;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  font-size: var(--font-regular);
}

.ant-breadcrumb a {
  font-size: 0.8rem;
}

.ant-typography.ant-typography-disabled * {
  color: rgba(0, 0, 0, 0.25) !important;
}

.ant-select-selector {
  border: none !important;
  box-shadow: none !important;
}

.ant-select-selector input:focus {
  box-shadow: none !important;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-state h2 {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.empty-state p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 1rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mt-5 {
  margin-top: 2rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mb-5 {
  margin-bottom: 2rem;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.component-modal {
  background-color: white;
  border-radius: var(--border-radius-lg);
  width: 500px;
  max-width: 90%;
  max-height: calc(100vh - 70px);
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.padding-lg {
  padding: var(--spacing-lg);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-light);
}

.modal-header h2 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  font-family: 'Sora', sans-serif;
}

.modal-close-icon {
  position: absolute;
  right: 16px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f0f0f0;
  transition: background-color 0.2s;
}

.modal-close-icon:hover {
  background-color: #f4d8d8;
}

.modal-close-icon svg {
  width: 16px;
  height: 16px;
  color: #666;
}

.required {
  color: #dc2626;
  margin-left: 2px;
}

.modal-form-group {
  margin-bottom: 14px;
}

.modal-form-row {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.modal-form-row .modal-form-group {
  flex: 1;
  min-width: 0;
}

.modal-form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #555;
}

.modal-form-group input,
.modal-form-group select,
.modal-form-group textarea,
.modal-form-input {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: var(--font-regular);
}

.modal-form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-error {
  color: var(--error-color);
  margin-bottom: 16px;
  font-size: var(--font-regular);
  margin-top: -8px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.btn-primary,
.btn-secondary,
.btn-black {
  padding: 6px 14px;
  border-radius: 4px;
  font-size: var(--font-regular);
  cursor: pointer;
  transition: background-color 0.2s, box-shadow 0.2s;
  font-family: 'Poppins', sans-serif;
}

.btn-secondary {
  background-color: #f5f5f5;
  border: 1px solid var(--border-color);
  color: #555;
}

.btn-primary {
  background-color: var(--primary-color);
  border: none;
  color: white;
}

.btn-black {
  background-color: var(--primary-black);
  border: none;
  color: white;
}

.btn-secondary:hover,
.btn-primary:hover,
.btn-black:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-primary:hover {
  background-color: var(--primary-black);
}

.btn-black:hover {
  background-color: black;
}

.btn-primary:disabled,
.btn-secondary:disabled,
.btn-black:disabled {
  background-color: #ccc !important;
  border: 1px solid #bbb !important;
  color: #888 !important;
  cursor: not-allowed;
  box-shadow: none !important;
}

.coming-soon-container {
  text-align: center;
  margin: auto !important;
}

@media (max-width: 1023px) {
  .dashboard-header .dashboard-actions,
  .dashboard-header .view-toggle {
    display: none;
  }
}
