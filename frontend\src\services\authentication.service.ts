import { COMMON_BASE_URL } from '../../config';
import httpService from './http.service';

// Define API endpoints for authentication
const API_ENDPOINTS = {
  SIGN_UP: '/api/auth/signUp',
  SIGN_IN: '/api/auth/signIn',
  RESET_PASSWORD: '/api/auth/resetPassword',
  FORGOT_PASSWORD: '/api/auth/forgotPassword',
  REFRESH_TOKEN: '/api/auth/refreshToken',
  OAUT2_CALLBACK_FOR_ACCESS_TOKEN: '/api/auth/oauth2/oauth/callback?code=',
  
};

export const AuthenticationService = {
  /**
   * Sign up a new user
   */
  signUp: async ({ fullName, email, password }: {
    fullName: string;
    email: string;
    password: string
  }): Promise<{ userId: string; message: string }> => {
    const signUpRequest = {
      username: email,
      password,
      fullName
    };
    const response = await httpService.post<{ userId: string; message: string }>(
      'common',
      API_ENDPOINTS.SIGN_UP,
      signUpRequest
    );
    return response;
  },
  /**
   * Sign in an existing user
   */
  signIn: async (username: string, password: string): Promise<{
    accessToken: string;
    refreshToken: string
  }> => {
    const signInRequest = { username, password };
    try {
      const response = await httpService.post<{
        accessToken: string;
        refreshToken: string
      }>('common', API_ENDPOINTS.SIGN_IN, signInRequest);

      // Handle 204 Unauthorized
      if (!response?.accessToken || !response?.refreshToken) {
        throw new Error('Invalid credentials. Provide valid credentials.');
      }

      return response;
    } catch (error: any) {

      // Handle 401 Unauthorized
      if (error.response?.status === 401) {
        throw new Error('Invalid credentials. Please check your username and password.');
      }

      // Handle other HTTP errors
      if (error.response?.status) {
        throw new Error(`Login failed with status ${error.response.status}. Please try again.`);
      }

      // Handle network errors or other exceptions
      throw new Error(error.message || 'An error occurred during login. Please try again.');
    }
  },

  /**
   * Reset password for a user
   */
  resetPassword: async (passwordResetToken: string, newPassword: string): Promise<{ success: boolean; message: string }> => {
    const resetPasswordRequest = { passwordResetToken, newPassword };
    const response = await httpService.post<{ success: boolean; message: string }>('common', API_ENDPOINTS.RESET_PASSWORD, resetPasswordRequest);
    return response;
  },

  /**
   * Forgot password
   */
  forgotPassword: async (email: string): Promise<{ success: boolean; message: string }> => {
    const response = await httpService.post<{ success: boolean; message: string }>('common', API_ENDPOINTS.FORGOT_PASSWORD, { email });
    return response;
  },

  /**
   * Refresh authentication token
   */
  refreshToken: async (refreshToken: string): Promise<{ 
    accessToken: string;
    refreshToken: string;
  }> => {
    const payload = { refreshToken };

    const response = await httpService.post<{ 
      accessToken: string;
      refreshToken: string;
    }>(
      'common',
      API_ENDPOINTS.REFRESH_TOKEN,
      payload
    );

    return response;
  },

  googleLogin: async (): Promise<void> => {
    const googleAuthUrl = COMMON_BASE_URL + '/api/auth/oauth2/google';
    window.location.href = googleAuthUrl;
  },

  handleGoogleCallback: async (code: string): Promise<{accessToken: string;refreshToken: string }> => {
    const response = await httpService.get<{accessToken: string; refreshToken: string }>('common', `${API_ENDPOINTS.OAUT2_CALLBACK_FOR_ACCESS_TOKEN}${code}`);
    return response;
  }
};

export default AuthenticationService;
