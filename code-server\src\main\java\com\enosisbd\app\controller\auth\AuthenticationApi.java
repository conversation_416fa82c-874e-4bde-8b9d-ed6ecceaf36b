package com.enosisbd.app.controller.auth;

import com.enosisbd.app.controller.exception.ProvideValidGitDataException;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface AuthenticationApi {
    @GetMapping("/login")
    ResponseEntity<String> initiateLogin(
            @RequestParam String gitRepositoryUrl,
            @RequestParam String gitUserEmail,
            @RequestParam(required = false) String redirectUrl,
            HttpServletRequest request) throws ProvideValidGitDataException;

    @GetMapping("/callback")
    ResponseEntity<String> handleCallback(HttpServletRequest request);
}
