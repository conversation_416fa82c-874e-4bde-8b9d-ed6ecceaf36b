# README

This README documents the steps necessary to get your application up and running.

---

## Pre-requisites

Before proceeding, ensure you have the following installed:

1. **Node.js (v22.13.0 or higher)**
2. **npm (v10.9.2 or higher)**
3. **JDK v17**
4. **PostgreSQL v16**
5. **Enosis VPN (v3)**
6. **IntelliJ IDEA (Community version is sufficient)**
7. **Maven 3.6.3 or higher**

---

## App Build Process (Test Automation Tool)

### Frontend (React)

1. Navigate to the frontend root folder:
   `cd ~/teststream/frontend`
2. Install dependencies:
   `npm install`
3. Start the development server:
   `npm run dev`

### Crawler (Node.js (Playwright))

1. Navigate to the Playwright root folder:
   `cd ~/teststream/backend/crawler/playwright`
2. Install dependencies:
   `npm install`
3. Start the Playwright development server:
   `npm run dev:playwright`

### Database (PostgreSQL)

1. Make sure you have PostgreSQL v16 installed.
2. Make sure to have/create a database named **tap** in PostgreSQL.
3. Make sure to update the url, username, password in the following file:
   `~/teststream/backend/api-server/src/main/resources/application.yaml`
   Example changes:
   ```
   url: ************************************
   username: postgres
   password: root
   ```
4. The database will be automatically updated with the necessary tables when the backend application is run since we are using JPA annotations for building local database.

### Backend (Spring Boot)

#### Building using command line

1. Make sure JAVA_HOME points to JDK 17 and added to the system path variables
2. Make sure you have Maven 3.6.3 or above installed and added to the system path variables
3. Navigate to the api-server root folder:
   `cd ~/teststream/backend/api-server`
4. Build the application:
   `mvn clean install`
5. Run the application:
   `mvn spring-boot:run`

#### Building using IntelliJ IDEA

1. Make sure you have Maven 3.6.3 or above installed and added to the system path variables
2. Open the `pom.xml` file in **IntelliJ IDEA** from folder:
   `~/teststream/backend/api-server`
3. Configure **Intellij IDEA** to run an application server where the configuration uses **JDK 17** and the main class is: `~/teststream/backend/api-server/src/main/java/com/enosisbd/api/server/ApiServerApplication.java`
4. Make sure the **lombok** plugin is installed in the **Intellij IDEA**.
5. Run the app in debug mode.

---

## Important Notes

- Ensure **Enosis VPN** is active while running and using the Test Automation tool.
- The **Ollama Server** is set up on a separate machine. Enosis VPN must be active to access them. The Dedicated server is active between **9:00 and 21:30 on weekdays**.

## Ollama Server (Spring AI, LLM Models)

More details regarding the Ollama server will be added later if needed.

---

For further assistance, refer to the documentation or contact the development team.

## User and Password for Login (for test purpose)

**ROLE_ADMIN**

- Username: `<EMAIL>`
- Password: `Admin1234!!`

**ROLE_USER**

- Username: `<EMAIL>`
- Password: `User1234!!`

For more information, refer to the `db/data` folder for user and role mappings.

---

## Liquibase Integration

When new entities or attributes are added, or any changes are made to existing entities, generate a Liquibase changeset for the corresponding database changes.

#### Generate Changeset

```shell
mvn clean install compile liquibase:diff "-Ddiff.version={new version number}"
```

#### For new project

```shell
mvn clean install compile liquibase:generateChangeLog "-Ddiff.version=001"
```

## Code-Server

Code Server is a powerful tool that allows developers to write, edit, and manage code through a web browser. With built-in Git integration, it supports version control workflows, making it ideal for remote development and collaborative coding.
Code Server has embedded spring boot application for managing files creation and deletion and git cloning and setting up environment.

#### To Run Code-Server Locally (Go to Code-Server Folder)

```shell
docker compose up -d --build --no-deps
```

#### From the code-server directory:

To start the embedded Spring server, run:

```shell
mvn spring-boot:run
```

### Sample URL Format will require to two fields gitRepository and gitUserEmail

- http://localhost:8081/auth/login?gitRepositoryUrl=[GitHubOrBitBucketRepositoryURL]&gitUserEmail=[EmailAddress]]

### Note

I encountered an issue with the code-server while testing it on the Ollama server for deployment.
I noticed that Bitbucket doesn’t allow HTTP for production-grade URLs—it only supports HTTP on loopback addresses like localhost or 127.0.0.1. However, GitHub does support HTTP for production URLs. Still, for better security, we should consider securing the server with HTTPS.


## Dockerize the whole application in a single docker-compose file
Currently, the `frontend`, `backend`, and `Playwright` applications are containerized using a single docker-compose.yaml file at the project root. As the project grows, we plan to gradually add additional services like code-server, automation, and llm-server to this same configuration.
To streamline deployment, we've set up separate Docker Compose files for different environments:

### Local Environment:
   File: `docker-compose.yaml`
   
   Command to execute: 
   ```shell
      docker compose -f docker-compose.yaml up -d --build
   ```

   Command to stop and remove all containers:
   ```shell
      docker compose -f docker-compose.yaml down
   ```

### Production Environment:
   File: `docker-compose.prod.yaml`

   Command to execute: 
   ```shell
      docker compose -f docker-compose.prod.yaml up -d --build
   ```

   To stop and remove all containers:
   ```shell
      docker compose -f docker-compose.prod.yaml down
   ```
