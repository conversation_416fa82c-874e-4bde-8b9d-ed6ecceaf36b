import { Link } from 'react-router-dom';
import { useAuth } from '../ContextApi/useAuth';
import './Toolbar.css';
import UserProfile from './UserProfile';

export const Toolbar = () => {
  const { logout, roles, isAuthenticated } = useAuth();

  return (
    <header className="toolbar">
      <div className="toolbar-container">
        <div className="logo">
          <Link to="/projects">
            <h1>TAP</h1>
          </Link>
        </div>

        {isAuthenticated && (
          <>
            <nav className="nav-links">
              <ul>
                <li>
                  <Link to="/projects">Projects</Link>
                </li>
                {roles?.includes('ROLE_ADMIN') && (
                  <li>
                    <Link to="/users">Users</Link>
                  </li>
                )}
              </ul>
            </nav>
            <UserProfile logout={logout} />
          </>
        )}
      </div>
    </header>
  );
};
