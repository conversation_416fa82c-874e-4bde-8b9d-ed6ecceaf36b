.share-modal {
  background-color: white;
  border-radius: var(--border-radius-lg);
  width: 480px;
  max-width: 90%;
  height: auto; /* Slightly reduced fixed height for better proportions */
  min-height: 350px; /* Ensure minimum height is maintained */
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  animation: slideIn 0.3s ease-out;
  border: 1px solid var(--border-color);
}

@keyframes slideIn {
  from {
    transform: translateY(10px);
    opacity: 0.8;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.share-modal-content {
  padding: var(--spacing-lg);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  background-color: white;
  height: auto; /* Dynamic height */
  min-height: 320px; /* Reduced minimum height */
  flex: 1; /* Take up available space */
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-400) transparent;
}

.share-modal-content::-webkit-scrollbar {
  width: 4px;
}

.share-modal-content::-webkit-scrollbar-track {
  background: transparent;
}

.share-modal-content::-webkit-scrollbar-thumb {
  background-color: var(--color-gray-400);
  border-radius: 4px;
}

.share-modal-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-gray-500);
}

.share-modal-search-section {
  position: relative;
  margin-bottom: var(--spacing-sm);
}

.share-modal-search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.share-modal-search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: var(--icon-size-sm);
}

.share-modal-search-input {
  width: 100%;
  padding: 8px 10px 8px 32px;
  border: 1px solid var(--input-border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  transition: all 0.2s ease;
  background-color: white;
}

.share-modal-search-input::placeholder {
  color: var(--placeholder-color);
}

.share-modal-search-input:disabled {
  background-color: var(--disabled-bg);
  cursor: not-allowed;
  color: var(--disabled-color);
}

.share-modal-search-spinner {
  position: absolute;
  right: 12px;
}

.share-modal-search-results {
  margin-top: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  max-height: 180px;
  overflow-y: auto;
  list-style: none;
  padding: 2px 0;
  background: white;
  box-shadow: var(--box-shadow-default);
  position: absolute;
  width: 100%;
  z-index: 10;
  animation: fadeDown 0.2s ease-out;
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-400) transparent;
}

.share-modal-search-results::-webkit-scrollbar {
  width: 4px;
}

.share-modal-search-results::-webkit-scrollbar-track {
  background: transparent;
}

.share-modal-search-results::-webkit-scrollbar-thumb {
  background-color: var(--color-gray-400);
  border-radius: 4px;
}

.share-modal-search-results::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-gray-500);
}

@keyframes fadeDown {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.share-modal-search-results li {
  padding: 7px 10px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 2px solid transparent;
}

.share-modal-search-results li:hover {
  background-color: var(--color-gray-100);
  border-left: 4px solid var(--primary-color);
}

.share-modal-no-results-item {
  padding: 10px 12px;
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  background-color: var(--color-gray-100);
  cursor: default;
}

.share-modal-shared-users-section {
  margin-top: var(--spacing-sm);
  min-height: 180px; /* Slightly reduced minimum height */
  flex: 1; /* Take up available space */
  display: flex;
  flex-direction: column;
}

.share-modal-shared-users-section h3 {
  margin: 0;
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
  font-family: 'Sora', sans-serif;
  padding-bottom: var(--spacing-sm);
}

.share-modal-shared-users-list {
  list-style: none;
  padding: 0;
  margin: 0;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  max-height: calc(4 * 57px); /* Height for 4 users */
  min-height: 120px; /* Minimum height */
  flex: 1; /* Take up available space */
  overflow-y: auto;
  background-color: white;
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-400) transparent;
}

.share-modal-shared-users-list::-webkit-scrollbar {
  width: 4px;
}

.share-modal-shared-users-list::-webkit-scrollbar-track {
  background: transparent;
}

.share-modal-shared-users-list::-webkit-scrollbar-thumb {
  background-color: var(--color-gray-400);
  border-radius: 4px;
}

.share-modal-shared-users-list::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-gray-500);
}

.share-modal-shared-users-list li {
  padding: 7px 10px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s;
}

.share-modal-shared-users-list li:hover {
  background-color: var(--color-gray-100);
}

.share-modal-shared-users-list li:last-child {
  border-bottom: none;
}

.share-modal-user-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--color-gray-100);
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
  font-size: var(--icon-size-sm);
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
}

.share-modal-search-results li:hover .share-modal-user-icon,
.share-modal-shared-users-list li:hover .share-modal-user-icon {
  background-color: var(--color-gray-200);
  transform: scale(1.05);
}

.share-modal-user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.share-modal-user-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.share-modal-user-email {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.share-modal-user-deactivated {
  font-size: var(--font-size-sm);
  color: var(--error-color);
  font-style: italic;
}

.share-modal-remove-user-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  transition: all 0.2s;
  opacity: 0.8;
}

.share-modal-remove-user-button:hover {
  background-color: rgba(220, 38, 38, 0.1);
  opacity: 1;
}

.share-modal-remove-user-button:disabled {
  color: var(--disabled-color);
  cursor: not-allowed;
  background-color: transparent;
  opacity: 0.5;
}

.share-modal-no-shared-users {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  border: 1px dashed var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--color-gray-100);
  min-height: 120px; /* Match minimum height of shared-users-list */
  flex: 1; /* Take up available space */
  display: flex;
  align-items: center;
  justify-content: center;
}
