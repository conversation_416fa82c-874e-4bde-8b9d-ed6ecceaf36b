package com.enosisbd.app.service.container;

import com.enosisbd.app.service.ContainerService;
import jakarta.annotation.PreDestroy;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Log4j2
@Service
@RequiredArgsConstructor
public class ContainerServiceImpl implements ContainerService {

    private static final String SETUP_SCRIPT_PATH = "/usr/local/environment/setup_user_repo.sh";
    private static final String DESTROY_SCRIPT_PATH = "/usr/local/environment/destroy_user_repo.sh";
    private static final String DELETE_SCRIPT_PATH = "/usr/local/environment/delete_repo.sh";

    @Value("${code-server.url}")
    private String codeServerRedirectionUrl;

    private final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

    @Override
    public ResponseEntity<String> start(String uuid, String gitRepositoryUrl, String gitUserEmail,
                                        String accessToken, String provider) {
        try {
            String repository = extractRepoName(gitRepositoryUrl);

            if (!isGitTokenValid(gitRepositoryUrl, accessToken, provider)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(
                        "You don't have access to the repository, please ask your manager to give access to repository.");
            }

            ProcessBuilder pb = new ProcessBuilder(
                    "bash",
                    SETUP_SCRIPT_PATH,
                    uuid,
                    repository,
                    gitRepositoryUrl,
                    gitUserEmail,
                    accessToken,
                    provider
            );

            pb.redirectErrorStream(true);
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            StringBuilder output = new StringBuilder();

            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            int exitCode = process.waitFor();

            if (exitCode == 0) {
                // Determine which proxy URL to use based on the provider
                String redirectUrl = getRedirectString(uuid, provider, repository);

                HttpHeaders headers = new HttpHeaders();
                headers.setLocation(URI.create(redirectUrl));

                executorService.schedule(() -> deleteRepositoryFromServer(uuid, repository), 1,
                        TimeUnit.HOURS);

                return ResponseEntity
                        .status(HttpStatus.FOUND)
                        .headers(headers)
                        .body("Repo setup successfully!\n" + output);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Error setting up repo!\n" + output);
            }
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    private String getRedirectString(String uuid, String provider, String repository) {
        String proxyUrl;
        if ("bitbucket".equalsIgnoreCase(provider)) {
            // Use the Bitbucket OAuth2 proxy URL (port 8083)
            proxyUrl = codeServerRedirectionUrl.replace(":8082", ":8083");
        } else {
            // Use the GitHub OAuth2 proxy URL (default port 8082)
            proxyUrl = codeServerRedirectionUrl;
        }

        return proxyUrl + "/?folder=/home/<USER>/projects/" + uuid + "/" + repository;
    }

    private void deleteRepositoryFromServer(String uuid, String repository) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                    "bash",
                    DELETE_SCRIPT_PATH,
                    uuid,
                    repository
            );
            pb.redirectErrorStream(true);
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                log.info("Deletion Log: {}", line);
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                log.error("Failed to delete repository {}/{}", uuid, repository);
            }
        } catch (IOException | InterruptedException e) {
            log.error("Error during repository deletion", e);
        }
    }

    public static String extractRepoName(String gitUrl) {
        Pattern pattern = Pattern.compile("([^/]+?)(?:\\.git)?$");
        Matcher matcher = pattern.matcher(gitUrl);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    @PreDestroy
    public void shutdown() {
        executorService.shutdown();
    }

    @Override
    public ResponseEntity<String> stop(String uuid, String repository) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                    "bash",
                    DESTROY_SCRIPT_PATH,
                    uuid
            );
            pb.redirectErrorStream(true);
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            StringBuilder output = new StringBuilder();

            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                return ResponseEntity.ok("Destroyed repository for user: " + uuid + "\n" + output);
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Failed to destroy repository for user: " + uuid);
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Exception: " + e.getMessage());
        }
    }


}
