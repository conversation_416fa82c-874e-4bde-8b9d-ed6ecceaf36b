import { LockOutlined, MailOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import AuthenticationService from '../../../services/authentication.service';
import { useAuth } from '../../ContextApi/useAuth';
import googleIcon from '../../../assets/google.svg';
import './AuthPage.css';

interface LoginValues {
  email: string;
  password: string;
}

const LoginPage: React.FC = () => {
  const [formValues, setFormValues] = useState<LoginValues>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Partial<LoginValues>>({});
  const [serverError, setServerError] = useState<string>('');
  const { login, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Check for OAuth error parameters
    const searchParams = new URLSearchParams(location.search);
    const oauthError = searchParams.get('error');
    
    if (oauthError === 'google_oauth_cancelled') {
      setServerError('Google login was cancelled');
    }
  }, [location]);

  if (isAuthenticated) {
    navigate('/projects');
  }

  const validate = (): boolean => {
    const newErrors: Partial<LoginValues> = {};

    if (!formValues.email.trim()) {
      newErrors.email = 'Please enter your email address';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formValues.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formValues.password) {
      newErrors.password = 'Please enter your password';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormValues((prev) => ({ ...prev, [name]: value }));
    if (errors[name as keyof LoginValues]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
    // Clear server error when user starts typing
    if (serverError) {
      setServerError('');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) return;
    // Clear server error when submitting again
    setServerError('');

    try {
      const { accessToken, refreshToken } = await AuthenticationService.signIn(
        formValues.email,
        formValues.password,
      );

      await login(accessToken, refreshToken);
      navigate('/projects');
    } catch (err: any) {
      setServerError(err.message || 'Invalid email or password');
    }
  };

  return (
    <div className="auth-container">
      <h3 className="auth-title">Welcome To TAP</h3>
      <p className="auth-subtitle">Let's login to your account</p>

      {/* Custom styled error alert */}
      {serverError && <div className="alert alert-error">{serverError}</div>}

      <form onSubmit={handleSubmit} className="auth-form">
        <div className="form-group">
          <label htmlFor="email">
            Email Address <span className="required">*</span>
          </label>
          <div className="input-container">
            <MailOutlined className="input-icon" />
            <input
              type="email"
              id="email"
              name="email"
              autoComplete="email"
              value={formValues.email}
              onChange={handleChange}
              placeholder="ex: <EMAIL>"
            />
          </div>
          {errors.email && <div className="auth-error">{errors.email}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="password">
            Password <span className="required">*</span>
          </label>
          <div className="input-container">
            <LockOutlined className="input-icon" />
            <input
              type="password"
              id="password"
              name="password"
              autoComplete="current-password"
              value={formValues.password}
              onChange={handleChange}
              placeholder="Enter your password"
            />
          </div>
          {errors.password && <div className="auth-error">{errors.password}</div>}
        </div>

        <div className="forgot-password" onClick={() => navigate('/forgot-password')}>
          Forgot password?
        </div>

        <button type="submit" className="btn-primary auth-button">
          Sign in
        </button>

        <div className="divider-container">
          <hr className="divider-line" />
          <span className="divider-text">or continue with</span>
          <hr className="divider-line" />
        </div>

        <button
          type="button"
          className="social-auth-button google-button"
          onClick={() => AuthenticationService.googleLogin()}
        >
          <img src={googleIcon} alt="Google" className="google-icon" />
          <span>Google</span>
        </button>

        <div className="auth-footer">
          <p>
            New Here?{' '}
            <Link to="/register" className="auth-link">
              Register
            </Link>
          </p>
        </div>
      </form>
    </div>
  );
};

export default LoginPage;
