.script-generation-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px); /* Adjust based on your header height */
  overflow: hidden;
}

.script-generation-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e8e8e8;
}

.script-generation-header h1 {
  margin: 0;
  font-size: 24px;
}

.script-generation-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.script-generation-tree {
  width: 300px;
  border-right: 1px solid #e8e8e8;
  padding: 16px;
  overflow-y: auto;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.tree-header h2 {
  margin: 0;
  font-size: 18px;
}

.tree-header-buttons {
  display: flex;
  gap: 8px;
}

.script-generation-code {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #e8e8e8;
}

.code-header h2 {
  margin: 0;
  font-size: 18px;
}

.code-header-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.no-script-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 24px;
  text-align: center;
  color: #888;
}

/* Make the tree nodes more readable */
.ant-tree-title {
  white-space: normal;
  word-break: break-word;
}

.streaming-code-container {
  position: relative;
  background: #ffffff;
  border-radius: 4px;
  overflow: hidden;
  height: 100%;
  max-height: calc(100vh - 80px);
  border: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
}

.streaming-code-block {
  margin: 0;
  padding: 1rem 0;
  font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-y: auto;
  overflow-x: hidden;
  color: #1e1e1e;
  background-color: #ffffff;
  height: 100%;
  min-height: 0;
}

.streaming-code-line {
  display: flex;
  width: 100%;
  min-height: 1.4em;
}

.streaming-line-number {
  flex: 0 0 auto;
  padding: 0 0.5rem;
  min-width: 30px; /* Increased from 2.5rem to accommodate larger numbers */
  text-align: right;
  color: #939393;
  user-select: none;
  border-right: 1px solid #e5e5e5;
  margin-right: 12px;
  position: sticky;
  left: 0;
  background: #ffffff;
}

.streaming-line-content {
  flex: 1;
  padding-right: 1rem;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #000000;
}

.streaming-code-block::-webkit-scrollbar {
  width: 14px;
}

.streaming-code-block::-webkit-scrollbar-track {
  background: #f3f3f3;
}

.streaming-code-block::-webkit-scrollbar-thumb {
  background-color: #cdcdcd;
  border: 3px solid #f3f3f3;
  border-radius: 7px;
}

.streaming-code-block::-webkit-scrollbar-thumb:hover {
  background-color: #a6a6a6;
}
