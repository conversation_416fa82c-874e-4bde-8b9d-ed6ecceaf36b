package com.enosisbd.api.server.controller.sharing;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.ShareEntityRequest;
import com.enosisbd.api.server.dto.UserDTO;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/sharing")
@RequiredArgsConstructor
public class SharingController implements SharingApi {

    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;

    /**
     * Get users who have access to an entity
     * This only returns users with direct non-inherited access, not users with inherited access
     */
    @GetMapping("/{entityType}/{entityId}/users")
    @RequiresEntityAccess(isDynamic = true)
    @Override
    public RestResponse<List<UserDTO>> getUsersWithAccess(
            @PathVariable String entityType,
            @PathVariable Long entityId) {

        EntityType type = EntityType.fromString(entityType);
        List<UserDTO> userDtos = entitySharingService.getUsersWithDirectAccessAsDto(type, entityId);
        return RestResponse.of(userDtos);
    }

    /**
     * Share an entity with a user
     */
    @PostMapping("/{entityType}/{entityId}/share")
    @RequiresEntityAccess(isDynamic = true)
    @Override
    public RestResponse<Boolean> shareEntity(
            @PathVariable String entityType,
            @PathVariable Long entityId,
            @Valid @RequestBody ShareEntityRequest request) {

        Long currentUserId = authorizationService.getCurrentUserId();
        EntityType type = EntityType.fromString(entityType);
        boolean result = entitySharingService.shareEntityByType(type, entityId, currentUserId, request.getUserId());
        return RestResponse.of(result);
    }

    /**
     * Remove a user's access to an entity
     */
    @DeleteMapping("/{entityType}/{entityId}/unshare/{userId}")
    @RequiresEntityAccess(isDynamic = true)
    @Override
    public RestResponse<Boolean> removeAccess(
            @PathVariable String entityType,
            @PathVariable Long entityId,
            @PathVariable Long userId) {

        EntityType type = EntityType.fromString(entityType);
        boolean result = entitySharingService.removeAccessByType(type, entityId, userId);
        return RestResponse.of(result);
    }

    /**
     * Search for users who can be shared with
     * Excludes admins, the entity creator, and users who already have access
     */
    @GetMapping("/users/search")
    @Override
    public RestResponse<List<UserDTO>> searchUsers(
            @RequestParam String query,
            @RequestParam(required = false) String entityType,
            @RequestParam(required = false) Long entityId) {

        Long currentUserId = authorizationService.getCurrentUserId();
        EntityType type = entityType != null ? EntityType.fromString(entityType) : null;
        List<UserDTO> userDtos = entitySharingService.searchUsersForSharing(query, currentUserId, type, entityId);
        return RestResponse.of(userDtos);
    }
}
