/* Card View */
.items-container.card-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.item.card-item {
  background: linear-gradient(to bottom right, #ffffff, #fafafa);
  border: 1px solid rgba(224, 224, 224, 0.7);
  border-radius: var(--radius-md);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  overflow: hidden;
}

.item.card-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-hover));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.item.card-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 20px -5px rgba(0, 0, 0, 0.1), 0 8px 16px -8px rgba(0, 0, 0, 0.08),
    inset 0 0 0 1px rgba(255, 255, 255, 0.7);
}

.item.card-item:hover::before {
  opacity: 1;
}

.item.card-item:active {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px -3px rgba(0, 0, 0, 0.08), 0 4px 8px -6px rgba(0, 0, 0, 0.06);
}

.item .item-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

.item .item-description {
  color: #666;
  margin: 0 0 1rem 0;
  font-size: var(--font-regular);
}

.item .item-meta {
  display: flex;
  flex-direction: column;
  font-size: 0.75rem;
  color: #666;
  margin-top: auto;
}

.item .item-meta-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 0.5rem;
}

.item .item-actions {
  position: relative;
  right: auto;
  bottom: auto;
  display: inline-flex;
  gap: 2px;
}

.top-right-report-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding-left: 10px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.item.card-item:hover .top-right-report-button {
  opacity: 1;
}

.top-right-report-button .action-button {
  background-color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.top-right-report-button .action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background-color: var(--primary-black);
  color: white;
  border-color: var(--primary-black);
}

.add-card {
  background: #f5f5f5;
  border: 2px dashed var(--border-color-fc);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 150px;
}

.add-card:hover {
  background: #fde5e5;
  border-color: var(--primary-color);
  font-weight: 500;
}

.add-card-content {
  text-align: center;
  color: #757575;
}

.add-card-content i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}
