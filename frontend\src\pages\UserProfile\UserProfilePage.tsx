import { message, Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { AlertCircleIcon, CardViewIcon, ListViewIcon } from '../../components/icons/IconLibrary';
import useUserPreferences from '../../hooks/useUserPreferences';
import { useAuth } from '../../components/ContextApi/useAuth';
import lang from '../../languages/lang';
import UserProfileService from '../../services/user-profile.service';
import { EntityViewMode, PasswordChange, UserProfile } from '../../types/user-profile.types';
import './UserProfilePage.css';

const UserProfilePage: React.FC = () => {
  // Get user preferences from our hook
  const { preferences, setViewMode } = useUserPreferences();
  const { user } = useAuth();
  
  // Check if user is authenticated via Google
  const isGoogleUser = user?.authProvider === 'GOOGLE';

  // Profile state
  const [profile, setProfile] = useState<UserProfile>({
    id: 0,
    fullName: '',
    email: '',
  });
  const [originalProfile, setOriginalProfile] = useState<UserProfile>({
    id: 0,
    fullName: '',
    email: '',
  });
  const [isProfileChanged, setIsProfileChanged] = useState<boolean>(false);
  const [profileErrors, setProfileErrors] = useState<{ fullName?: string }>({});

  // Password state
  const [passwordData, setPasswordData] = useState<PasswordChange>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [passwordErrors, setPasswordErrors] = useState<{
    currentPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
    server?: string;
  }>({});

  // Loading states
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSavingPreferences, setIsSavingPreferences] = useState<boolean>(false);
  const isDisableChangePasswordButton =
    !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword;

  // Fetch profile data on component mount
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setIsLoading(true);
        const profileData = await UserProfileService.getCurrentUserProfile();

        // Update local state
        setProfile(profileData);
        setOriginalProfile(profileData);
      } catch (error) {
        console.error('Error fetching user profile:', error);
        message.error(lang.USER_PROFILE.FETCH_ERROR);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, []);

  // Handle profile form changes
  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const updatedProfile = { ...profile, [name]: value };
    setProfile(updatedProfile);
    setProfileErrors({});
    setIsProfileChanged(updatedProfile.fullName !== originalProfile.fullName);
  };

  // Validate profile form
  const validateProfile = (): boolean => {
    const errors: { fullName?: string } = {};

    if (profile.fullName.length < 4) {
      errors.fullName = lang.USER_PROFILE.FULLNAME_VALIDATION_MIN_LENGTH;
    } else if (profile.fullName.length > 255) {
      errors.fullName = lang.USER_PROFILE.FULLNAME_VALIDATION_MAX_LENGTH;
    }

    setProfileErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle profile form submission
  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateProfile()) return;

    try {
      setIsLoading(true);
      const updatedProfile = await UserProfileService.updateUserProfile(profile);
      setProfile(updatedProfile);
      setOriginalProfile(updatedProfile);
      setIsProfileChanged(false);
      message.success(lang.USER_PROFILE.PROFILE_UPDATE_SUCCESS);
    } catch (error) {
      console.error('Error updating profile:', error);
      setProfileErrors({ fullName: lang.USER_PROFILE.PROFILE_UPDATE_ERROR });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle password form changes
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({ ...prev, [name]: value }));
    setPasswordErrors({});
  };

  // Validate password form
  const validatePassword = (): boolean => {
    const errors: {
      currentPassword?: string;
      newPassword?: string;
      confirmPassword?: string;
    } = {};

    const strongPasswordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

    if (passwordData.newPassword && !strongPasswordRegex.test(passwordData.newPassword)) {
      errors.newPassword = lang.USER_PROFILE.PASSWORD_VALIDATION;
    }

    if (
      passwordData.newPassword &&
      passwordData.confirmPassword &&
      passwordData.newPassword !== passwordData.confirmPassword
    ) {
      errors.confirmPassword = lang.USER_PROFILE.PASSWORD_VALIDATION_MATCH;
    }

    setPasswordErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle password form submission
  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePassword()) return;

    try {
      setIsLoading(true);
      const response = await UserProfileService.changePassword(passwordData);

      // Clear password fields
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      message.success(response.message || lang.USER_PROFILE.PASSWORD_CHANGE_SUCCESS);
    } catch (error) {
      console.error('Error changing password:', error);
      // Handle error response from API
      const errorMessage =
        error instanceof Error
          ? error.message
          : typeof error === 'object' && error !== null && 'response' in error
          ? (error as { response?: { data?: { message?: string } } })?.response?.data?.message ||
            lang.USER_PROFILE.PASSWORD_CHANGE_FAILED
          : lang.USER_PROFILE.PASSWORD_CHANGE_FAILED;

      setPasswordErrors({
        server: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle preference changes using our hook
  const handlePreferenceChange = async (viewMode: EntityViewMode) => {
    try {
      setIsSavingPreferences(true);
      await setViewMode(viewMode);
      message.success(lang.USER_PROFILE.PREFERENCES_SAVED_SUCCESS);
    } catch (error) {
      console.error('Error saving preferences:', error);
      message.error(lang.USER_PROFILE.PREFERENCES_SAVED_ERROR);
    } finally {
      setIsSavingPreferences(false);
    }
  };

  return (
    <Spin spinning={isLoading}>
      <div className="user-profile-page-container">
        <h2 className="dashboardHeaderPadding mb-3">{lang.USER_PROFILE.TITLE}</h2>

        <div className="user-profile-page-content">
          {/* Left Column */}
          <div className="user-profile-page-column">
            <div className="user-profile-page-section">
              <h2>{lang.USER_PROFILE.PROFILE_INFO}</h2>

              <form onSubmit={handleProfileSubmit}>
                <div className="user-profile-page-form-group">
                  <label htmlFor="fullName">
                    {lang.USER_PROFILE.FULL_NAME}{' '}
                    <span className="user-profile-page-required">*</span>
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    value={profile.fullName}
                    onChange={handleProfileChange}
                    placeholder={lang.USER_PROFILE.FULL_NAME_PLACEHOLDER}
                    autoFocus
                  />
                  {profileErrors.fullName && (
                    <div className="user-profile-page-error">{profileErrors.fullName}</div>
                  )}
                </div>

                <div className="user-profile-page-form-group">
                  <label htmlFor="email">{lang.USER_PROFILE.EMAIL}</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={profile.email}
                    disabled
                    readOnly
                    className="user-profile-page-readonly-input"
                  />
                  <small className="user-profile-page-input-help">
                    {lang.USER_PROFILE.EMAIL_MESSAGE}
                  </small>
                </div>

                <button
                  type="submit"
                  className="btn-primary"
                  disabled={!isProfileChanged || !profile.fullName.trim()}
                >
                  {lang.USER_PROFILE.SAVE_CHANGES}
                </button>
              </form>
            </div>
            <div className="user-profile-page-section">
              <h2>{lang.USER_PROFILE.CHANGE_PASSWORD}</h2>
              {isGoogleUser ? (
                <p className="loading">{lang.USER_PROFILE.GOOGLE_AUTH_PASSWORD_MESSAGE}</p>
              ) : (
                <>
                  {passwordErrors.server && (
                    <div className="user-profile-page-error user-profile-page-error-server">
                      <AlertCircleIcon />
                      {passwordErrors.server}
                    </div>
                  )}

                  <form onSubmit={handlePasswordSubmit}>
                    <div className="user-profile-page-form-group">
                      <label htmlFor="currentPassword">
                        {lang.USER_PROFILE.CURRENT_PASSWORD}{' '}
                        <span className="user-profile-page-required">*</span>
                      </label>
                      <input
                        type="password"
                        id="currentPassword"
                        name="currentPassword"
                        value={passwordData.currentPassword}
                        onChange={handlePasswordChange}
                        placeholder={lang.USER_PROFILE.CURRENT_PASSWORD_PLACEHOLDER}
                      />
                      {passwordErrors.currentPassword && (
                        <div className="user-profile-page-error">{passwordErrors.currentPassword}</div>
                      )}
                    </div>

                    <div className="user-profile-page-form-group">
                      <label htmlFor="newPassword">
                        {lang.USER_PROFILE.NEW_PASSWORD}{' '}
                        <span className="user-profile-page-required">*</span>
                      </label>
                      <input
                        type="password"
                        id="newPassword"
                        name="newPassword"
                        value={passwordData.newPassword}
                        onChange={handlePasswordChange}
                        placeholder={lang.USER_PROFILE.NEW_PASSWORD_PLACEHOLDER}
                      />
                      {passwordErrors.newPassword && (
                        <div className="user-profile-page-error">{passwordErrors.newPassword}</div>
                      )}
                    </div>

                    <div className="user-profile-page-form-group">
                      <label htmlFor="confirmPassword">
                        {lang.USER_PROFILE.CONFIRM_NEW_PASSWORD}{' '}
                        <span className="user-profile-page-required">*</span>
                      </label>
                      <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={passwordData.confirmPassword}
                        onChange={handlePasswordChange}
                        placeholder={lang.USER_PROFILE.CONFIRM_NEW_PASSWORD_PLACEHOLDER}
                      />
                      {passwordErrors.confirmPassword && (
                        <div className="user-profile-page-error">{passwordErrors.confirmPassword}</div>
                      )}
                    </div>

                    <button
                      type="submit"
                      className="btn-primary"
                      disabled={isDisableChangePasswordButton}
                    >
                      {lang.USER_PROFILE.CHANGE_PASSWORD}
                    </button>
                  </form>
                </>
              )}
            </div>

          </div>

          {/* Right Column */}
          <div className="user-profile-page-column">
            <div className="user-profile-page-section">
              <h2>{lang.USER_PROFILE.PREFERENCES}</h2>
              <div className="user-profile-page-setting-group">
                <h3>{lang.USER_PROFILE.DEFAULT_VIEW_MODE}</h3>
                <p className="user-profile-page-setting-description">
                  {lang.USER_PROFILE.DEFAULT_VIEW_MODE_DESC}
                </p>

                <div className="user-profile-page-view-mode-toggle">
                  <button
                    type="button"
                    className={`user-profile-page-view-mode-button ${
                      preferences?.entityViewMode === 'card' ? 'active' : ''
                    }`}
                    onClick={() => handlePreferenceChange('card')}
                    disabled={isSavingPreferences}
                  >
                    <CardViewIcon />
                    {lang.USER_PROFILE.CARD_VIEW}
                  </button>

                  <button
                    type="button"
                    className={`user-profile-page-view-mode-button ${
                      preferences?.entityViewMode === 'table' ? 'active' : ''
                    }`}
                    onClick={() => handlePreferenceChange('table')}
                    disabled={isSavingPreferences}
                  >
                    <ListViewIcon />
                    {lang.USER_PROFILE.LIST_VIEW}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default UserProfilePage;
