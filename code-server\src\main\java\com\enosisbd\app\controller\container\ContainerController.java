package com.enosisbd.app.controller.container;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.enosisbd.app.service.ContainerService;
import com.enosisbd.app.util.GitProviderUtils;
import com.enosisbd.app.util.GitProviderUtils.GitProvider;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

@RestController
public class ContainerController implements ContainerApi {

  private static final String GIT_REPO_URL_SESSION_KEY = "gitRepositoryUrl";
  private static final String GIT_USER_EMAIL_SESSION_KEY = "gitUserEmail";

  @Autowired
  private ContainerService containerService;

  @Override
  @GetMapping("/container/start")
  public ResponseEntity<String> start(@AuthenticationPrincipal OAuth2User oauth2User,
      @RequestParam(required = false) String gitRepositoryUrl,
      @RequestParam(required = false) String gitUserEmail,
      HttpServletRequest request) {

    // Get repository URL and email from request parameters or session
    HttpSession session = request.getSession(false);

    if (gitRepositoryUrl == null && session != null) {
      gitRepositoryUrl = (String) session.getAttribute(GIT_REPO_URL_SESSION_KEY);
    }

    if (gitUserEmail == null && session != null) {
      gitUserEmail = (String) session.getAttribute(GIT_USER_EMAIL_SESSION_KEY);
    }

    // If we still don't have the required parameters, return an error
    if (gitRepositoryUrl == null || gitUserEmail == null) {
      return ResponseEntity.badRequest().body("Missing required parameters: gitRepositoryUrl and gitUserEmail");
    }

    // Get the access token from the OAuth2User
    String accessToken = oauth2User.getAttribute("access_token");

    // Get the authentication provider from the authentication token
    String provider = "github"; // Default to GitHub
    if (oauth2User.getAttributes().containsKey("clientRegistrationId")) {
      provider = oauth2User.getAttribute("clientRegistrationId");
    } else if (request.getUserPrincipal() instanceof OAuth2AuthenticationToken) {
      provider = ((OAuth2AuthenticationToken) request.getUserPrincipal()).getAuthorizedClientRegistrationId();
    } else {
      // Determine provider from repository URL if not available from authentication
      GitProvider gitProvider = GitProviderUtils.determineProvider(gitRepositoryUrl);
      if (gitProvider == GitProvider.BITBUCKET) {
        provider = "bitbucket";
      }
    }

    String uuid = UUID.randomUUID().toString();

    // Clear session attributes after use
    if (session != null) {
      session.removeAttribute(GIT_REPO_URL_SESSION_KEY);
      session.removeAttribute(GIT_USER_EMAIL_SESSION_KEY);
    }

    return this.containerService.start(uuid, gitRepositoryUrl, gitUserEmail, accessToken, provider);
  }

  @Override
  @GetMapping("/container/stop")
  public ResponseEntity<String> stop(@RequestParam String username,
      @RequestParam String repository) {
    return this.containerService.stop(username, repository);
  }
}
