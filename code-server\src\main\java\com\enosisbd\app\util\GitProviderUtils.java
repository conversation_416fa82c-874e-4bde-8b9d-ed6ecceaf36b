package com.enosisbd.app.util;

import java.util.regex.Pattern;

/**
 * Utility class for Git provider operations
 */
public class GitProviderUtils {

    private static final Pattern GITHUB_URL_PATTERN = Pattern.compile("github\\.com");
    private static final Pattern BITBUCKET_URL_PATTERN = Pattern.compile("bitbucket\\.org");

    /**
     * Enum representing supported Git providers
     */
    public enum GitProvider {
        GITHUB,
        BITBUCKET,
        UNKNOWN
    }

    /**
     * Determines the Git provider from a repository URL
     *
     * @param gitRepositoryUrl the Git repository URL
     * @return the identified GitProvider
     */
    public static GitProvider determineProvider(String gitRepositoryUrl) {
        if (gitRepositoryUrl == null || gitRepositoryUrl.isEmpty()) {
            return GitProvider.UNKNOWN;
        }

        if (GITHUB_URL_PATTERN.matcher(gitRepositoryUrl).find()) {
            return GitProvider.GITHUB;
        } else if (BITBUCKET_URL_PATTERN.matcher(gitRepositoryUrl).find()) {
            return GitProvider.BITBUCKET;
        }

        return GitProvider.UNKNOWN;
    }
}
