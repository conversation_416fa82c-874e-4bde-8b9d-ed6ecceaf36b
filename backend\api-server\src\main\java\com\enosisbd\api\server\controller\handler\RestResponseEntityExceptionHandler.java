package com.enosisbd.api.server.controller.handler;

import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.exception.ForbiddenRestException;
import com.enosisbd.api.server.exception.NotFoundRestException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.Map;

/**
 * The type Rest response entity exception handler.
 */
@ControllerAdvice
public class RestResponseEntityExceptionHandler {


    /**
     * Handle not found response entity.
     *
     * @param e the e
     * @return the response entity
     */
    @ExceptionHandler(NotFoundRestException.class)
    protected ResponseEntity<Object> handleNotFound(NotFoundRestException e) {
        var message = e.getMessage();
        if (message.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
        var errorBody = Map.of("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorBody);
    }

    @ExceptionHandler(ForbiddenRestException.class)
    protected ResponseEntity<Object> handleForbiddenRestException(ForbiddenRestException e) {
        var message = e.getMessage();
        if (message.isEmpty()) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }
        var errorBody = Map.of("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(errorBody);
    }

    /**
     * Handle user error response entity.
     *
     * @param e the e
     * @return the response entity
     */
    @ExceptionHandler(BadRequestRestException.class)
    protected ResponseEntity<Object> handleUserError(BadRequestRestException e) {
        var message = e.getMessage();
        if (message.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
        var errorBody = Map.of("message", e.getMessage());
        return ResponseEntity.badRequest().body(errorBody);
    }
}
