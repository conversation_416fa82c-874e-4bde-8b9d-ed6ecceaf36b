package com.enosisbd.api.server.controller.project;

import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.dto.ProjectDto;
import com.enosisbd.api.server.dto.TreeNodeDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.service.crawledPage.CrawledPageService;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.project.ProjectService;
import com.enosisbd.api.server.service.tree.ProjectTreeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProjectControllerTest {

    @Mock
    private ProjectService projectService;

    @Mock
    private ModuleService moduleService;

    @Mock
    private CrawledPageService crawledPageService;

    @Mock
    private ProjectTreeService projectTreeService;

    @InjectMocks
    private ProjectController projectController;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(projectController).build();
    }

    @Test
    void getProjectTree_ShouldReturnTreeStructure() {
        // Arrange
        Long projectId = 1L;
        
        // Create a tree structure
        TreeNodeDto rootNode = TreeNodeDto.builder()
                .id(projectId)
                .name("Test Project")
                .type(EntityType.PROJECT)
                .children(new ArrayList<>())
                .build();
        
        // Mock service call
        when(projectTreeService.getProjectTree(projectId)).thenReturn(rootNode);
        
        // Act
        RestResponse<TreeNodeDto> response = projectController.getProjectTree(projectId);
        
        // Assert
        assertNotNull(response);
        assertNotNull(response.getData());
        assertEquals(rootNode, response.getData());
        
        // Verify service call
        verify(projectTreeService).getProjectTree(projectId);
    }

    @Test
    void getProjectTree_ShouldPropagateExceptionWhenServiceThrows() {
        // Arrange
        Long projectId = 999L;
        String errorMessage = "Project not found with ID: " + projectId;
        
        when(projectTreeService.getProjectTree(projectId))
                .thenThrow(new NotFoundRestException(errorMessage));
        
        // Act & Assert
        NotFoundRestException exception = assertThrows(
            NotFoundRestException.class,
            () -> projectController.getProjectTree(projectId)
        );
        
        assertEquals(errorMessage, exception.getMessage());
        verify(projectTreeService).getProjectTree(projectId);
    }
}
