<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="tazbinur (generated)" id="1746693444482-1">
        <createTable tableName="crawled_page">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="crawled_pagePK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="crawl_option" type="JSONB"/>
            <column name="dom_json" type="JSONB"/>
            <column name="page_name" type="VARCHAR(255)"/>
            <column name="page_url" type="VARCHAR(255)"/>
            <column name="test_suite_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-2">
        <createTable tableName="entity_access">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="entity_accessPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="is_inherited" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="entity_type_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="shared_by_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="shared_with_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-3">
        <createTable tableName="password_reset_tokens">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="password_reset_tokensPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="expiry_date" type="TIMESTAMP(6) WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="token" type="VARCHAR(255)"/>
            <column name="used" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-4">
        <createTable tableName="project">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="projectPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="description" type="TEXT"/>
            <column name="git_platform_email" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="repository_url" type="VARCHAR(2048)"/>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-5">
        <createTable tableName="roles">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="rolesPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="name" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-6">
        <createTable tableName="sharable_entity_types">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="sharable_entity_typesPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="is_active" type="BOOLEAN"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-7">
        <createTable tableName="test_case">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="test_casePK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="description" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="expected_result" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)"/>
            <column name="steps" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="test_case_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="test_suite_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-8">
        <createTable tableName="test_script">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="test_scriptPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="code" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="code_execution_logs" type="TEXT"/>
            <column name="last_execution_time" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="number_of_failed_test_cases" type="INTEGER"/>
            <column name="number_of_passed_test_cases" type="INTEGER"/>
            <column name="number_of_test_cases" type="INTEGER"/>
            <column name="test_suite_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-9">
        <createTable tableName="test_suite">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="test_suitePK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="requirement" type="TEXT"/>
            <column name="title" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="version_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-10">
        <createTable tableName="user_preferences">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="user_preferencesPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="entity_view_mode" type="VARCHAR(10)"/>
            <column name="user_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-11">
        <createTable tableName="users">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="usersPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="approved" type="BOOLEAN"/>
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="full_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="password" type="VARCHAR(100)"/>
            <column name="picture_url" type="VARCHAR(512)"/>
            <column name="provider" type="VARCHAR(10)"/>
            <column name="provider_id" type="VARCHAR(25)"/>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-12">
        <createTable tableName="users_roles">
            <column name="user_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="users_rolesPK"/>
            </column>
            <column name="role_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="users_rolesPK"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-13">
        <createTable tableName="version">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="versionPK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="language" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="platform" type="VARCHAR(255)"/>
            <column name="test_case_generation_method" type="VARCHAR(255)"/>
            <column name="project_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-14">
        <addUniqueConstraint columnNames="user_id" constraintName="UC_PASSWORD_RESET_TOKENSUSER_ID_COL" tableName="password_reset_tokens"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-15">
        <addUniqueConstraint columnNames="name" constraintName="UC_SHARABLE_ENTITY_TYPESNAME_COL" tableName="sharable_entity_types"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-16">
        <addUniqueConstraint columnNames="test_suite_id" constraintName="UC_TEST_SCRIPTTEST_SUITE_ID_COL" tableName="test_script"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-17">
        <addUniqueConstraint columnNames="email" constraintName="UC_USERSEMAIL_COL" tableName="users"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-18">
        <addUniqueConstraint columnNames="user_id" constraintName="UC_USER_PREFERENCESUSER_ID_COL" tableName="user_preferences"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-19">
        <addUniqueConstraint columnNames="shared_with_user_id, entity_type_id, entity_id" constraintName="UKh9qxdc8xagiuvinoqmb25fioe" tableName="entity_access"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-20">
        <createIndex indexName="idx_crawled_page_test_suite" tableName="crawled_page">
            <column name="test_suite_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-21">
        <createIndex indexName="idx_entity_access_entity" tableName="entity_access">
            <column name="entity_type_id"/>
            <column name="entity_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-22">
        <createIndex indexName="idx_entity_access_is_inherited" tableName="entity_access">
            <column name="shared_with_user_id"/>
            <column name="is_inherited"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-23">
        <createIndex indexName="idx_password_reset_token" tableName="password_reset_tokens">
            <column name="token"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-24">
        <createIndex indexName="idx_project_updated_at" tableName="project">
            <column name="updated_at"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-25">
        <createIndex indexName="idx_roles_name" tableName="roles">
            <column name="name"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-26">
        <createIndex indexName="idx_sharable_entity_type_name_active" tableName="sharable_entity_types">
            <column name="name"/>
            <column name="is_active"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-27">
        <createIndex indexName="idx_test_case_test_suite_id" tableName="test_case">
            <column name="test_suite_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-28">
        <createIndex indexName="idx_test_suite_updated_at" tableName="test_suite">
            <column name="updated_at"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-29">
        <createIndex indexName="idx_test_suite_version" tableName="test_suite">
            <column name="version_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-30">
        <createIndex indexName="idx_users_approved" tableName="users">
            <column name="approved"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-31">
        <createIndex indexName="idx_users_full_name" tableName="users">
            <column name="full_name"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-32">
        <createIndex indexName="idx_version_project" tableName="version">
            <column name="project_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-33">
        <createIndex indexName="idx_version_updated_at" tableName="version">
            <column name="updated_at"/>
        </createIndex>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-34">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="users_roles" constraintName="FK2o0jvgh89lemvvo17cbqvdxaa" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-35">
        <addForeignKeyConstraint baseColumnNames="version_id" baseTableName="test_suite" constraintName="FK3wq5c8i0ygh18lxpmk04p5b4n" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="version" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-36">
        <addForeignKeyConstraint baseColumnNames="test_suite_id" baseTableName="test_script" constraintName="FK5ns6tem7vclikpotk12ggg5sh" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="test_suite" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-37">
        <addForeignKeyConstraint baseColumnNames="project_id" baseTableName="version" constraintName="FK5q7csydn4alo2pf0bbv74g9ko" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="project" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-38">
        <addForeignKeyConstraint baseColumnNames="shared_with_user_id" baseTableName="entity_access" constraintName="FK779cc7q41iveynrat7dtodkcm" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-39">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="user_preferences" constraintName="FKepakpib0qnm82vmaiismkqf88" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-40">
        <addForeignKeyConstraint baseColumnNames="shared_by_user_id" baseTableName="entity_access" constraintName="FKguwfqpfba6ygse0dirapvtykk" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-41">
        <addForeignKeyConstraint baseColumnNames="test_suite_id" baseTableName="test_case" constraintName="FKhjc8d84sw3w3oe3ccnnlj3rde" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="test_suite" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-42">
        <addForeignKeyConstraint baseColumnNames="role_id" baseTableName="users_roles" constraintName="FKj6m8fwv7oqv74fcehir1a9ffy" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="roles" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-43">
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="password_reset_tokens" constraintName="FKk3ndxg5xp6v7wd4gjyusp15gq" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="users" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-44">
        <addForeignKeyConstraint baseColumnNames="test_suite_id" baseTableName="crawled_page" constraintName="FKmavq1eqfpuw8umgs0spg86l3p" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="test_suite" validate="true"/>
    </changeSet>
    <changeSet author="tazbinur (generated)" id="1746693444482-45">
        <addForeignKeyConstraint baseColumnNames="entity_type_id" baseTableName="entity_access" constraintName="FKscw42yr9hbhchb9b6vskgkh9o" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="sharable_entity_types" validate="true"/>
    </changeSet>
</databaseChangeLog>
