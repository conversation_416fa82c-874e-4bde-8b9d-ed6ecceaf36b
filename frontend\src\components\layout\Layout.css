.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background-default);
}

.app-header {
  flex-shrink: 0;
  background-color: var(--background-light);
  border-bottom: 1px solid var(--border-color);
}

.app-main {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 25%, #f1f5f9 75%, #e2e8f0 100%);
  position: relative;
}

.app-main::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 0% 0%, rgba(234, 42, 53, 0.03) 0%, transparent 60%),
    radial-gradient(circle at 100% 0%, rgba(32, 36, 48, 0.03) 0%, transparent 60%),
    radial-gradient(circle at 50% 100%, rgba(234, 42, 53, 0.02) 0%, transparent 60%);
  pointer-events: none;
}

.app-main > * {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 1rem;
}

.app-footer {
  flex-shrink: 0;
  background-color: var(--background-light);
  border-top: 1px solid var(--border-color);
  text-align: center;
  font-size: var(--font-regular);
  color: var(--text-secondary);
}

@media (max-width: 640px) {
  .app-main > * {
    padding: 0.75rem;
  }
}
