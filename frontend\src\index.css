@import url('https://fonts.googleapis.com/css2?family=Sora:wght@400;700&family=Poppins:wght@400;500;700&display=swap');

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Colors */
  --primary-color: #ea2a35;
  --primary-hover: #b91c1c;
  --primary-black: #202430;
  --secondary-color: #0ea5e9;
  --secondary-hover: #0284c7;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --background-light: #f8fafc;
  --background-dark: #0f172a;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;

  font-family: 'Poppins', system-ui, Avenir, Helvetica, Arial, sans-serif;
  --font-regular: 14px;
  --font-medium: 16px;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: white;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
}

html {
  font-size: var(--font-medium);
  height: 100%;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--background-light);
  color: var(--text-primary);
  line-height: 1.5;
}

h1,
h2,
h3,
h4 {
  font-family: 'Sora', serif;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
}

input,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
}

ul,
ol {
  list-style: none;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

#root {
  height: 100%;
}
