package com.enosisbd.app.filter;

import java.io.IOException;
import java.util.Optional;
import java.util.UUID;

import org.slf4j.MDC;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class RequestLoggingFilter extends OncePerRequestFilter {
    
    private String getClientIP(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null) {
            return request.getRemoteAddr();
        }
        return xfHeader.split(",")[0].trim();
    }

    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest req,
            @NonNull HttpServletResponse res,
            @NonNull FilterChain next
    ) throws ServletException, IOException {
        try {
            // Generate unique request ID
            String requestId = UUID.randomUUID().toString();
            
            // Get client IP
            String clientIP = getClientIP(req);

            // Set values in MDC
            MDC.put("requestId", requestId);
            MDC.put("clientIp", clientIP);

            var queryString = Optional.ofNullable(req.getQueryString())
                    .map(q -> String.format("?%s", q))
                    .orElse("");

            log.info("{} {}{}", req.getMethod(), req.getRequestURI(), queryString);
            log.info("Remote Address: {}", clientIP);

            next.doFilter(req, res);

            log.info("Status: {}, ContentType: {}", res.getStatus(), res.getContentType());
        } finally {
            MDC.clear();
        }
    }
}
