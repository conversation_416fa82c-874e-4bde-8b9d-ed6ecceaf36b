import {
  CopyOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import Editor, { Monaco } from '@monaco-editor/react';
import { Typography, message } from 'antd';
import * as monaco from 'monaco-editor';
import React, { useState } from 'react';
import { getFileExtension } from '../../constants/automation.constants';
import { Language, Platform } from '../../types/version.types';
import './GeneratedCodePage.css';

interface GeneratedCodePageProps {
  code: string;
  title?: string;
  language: Language;
  platform: Platform,
}

export const GeneratedCodePage: React.FC<GeneratedCodePageProps> = ({
  code: initialCode,
  title,
  language,
  platform,
}) => {
  const [displayedCode, setDisplayedCode] = useState(initialCode);
  const [error, setError] = useState<string>('');
  const [isDownloading, setIsDownloading] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(displayedCode);
      message.success('Code copied to clipboard');
    } catch (err) {
      message.error('Failed to copy code');
      console.error('Failed to copy code:', err);
    }
  };
  const handleDownload = () => {
    try {
      setIsDownloading(true);

      // Create a blob with the code content
      const blob = new Blob([displayedCode], { type: 'text/plain' });

      // Create a URL for the blob
      const url = URL.createObjectURL(blob);

      // Determine file name with correct extension
      const fileExtension = getFileExtension(language);
      const fileName = `generated-code${fileExtension}`;

      // Create a temporary anchor element
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;

      // Append to the document, click, and remove
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      // Release the URL object
      URL.revokeObjectURL(url);

      setTimeout(() => setIsDownloading(false), 2000);
      message.success('Download successful');
    } catch (err) {
      message.error('Download failed');
      console.error('Failed to download code:', err);
      setIsDownloading(false);
    }
  };

  const handleEditorDidMount = (editor: any, monacoEditor: Monaco) => {
    // Add type definitions for Selenium
    monaco.languages.typescript.typescriptDefaults.addExtraLib(
      `
      declare module 'selenium-webdriver' {
        export class Builder {
          forBrowser(browserName: string): Builder;
          build(): WebDriver;
        }
        export class WebDriver {
          findElement(by: By): WebElement;
          quit(): Promise<void>;
        }
        export class WebElement {
          click(): Promise<void>;
          sendKeys(text: string): Promise<void>;
          getText(): Promise<string>;
        }
        export class By {
          static css(selector: string): By;
          static xpath(xpath: string): By;
          static id(id: string): By;
        }
      }
    `,
      'selenium-webdriver.d.ts',
    );

    if (platform === 'Playwright') {
      monaco.languages.typescript.typescriptDefaults.addExtraLib(
        `
        declare module '@playwright/test' {
          export class Page {
            goto(url: string): Promise<void>;
            click(selector: string): Promise<void>;
            fill(selector: string, value: string): Promise<void>;
            locator(selector: string): Locator;
          }
          export class Locator {
            click(): Promise<void>;
            fill(value: string): Promise<void>;
            type(value: string): Promise<void>;
          }
          export function test(name: string, fn: (page: Page) => Promise<void>): void;
          export function expect(locator: Locator): {
            toBeVisible(): Promise<void>;
            toHaveText(text: string): Promise<void>;
          };
        }
      `,
        'playwright.d.ts',
      );
    }

    // Configure TypeScript compiler options
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      allowNonTsExtensions: true,
      typeRoots: ['node_modules/@types'],
      strict: true,
    });
  };

  const getEditorOptions = (): monaco.editor.IStandaloneEditorConstructionOptions => ({
    readOnly: true,
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    fontSize: 13,
    lineNumbers: 'on' as const,
    roundedSelection: false,
    lineHeight: 20,
    padding: { top: 8, bottom: 8 },
    automaticLayout: true,
    scrollbar: {
      vertical: 'visible',
      horizontal: 'visible',
      useShadows: false,
      horizontalScrollbarSize: 10,
      verticalScrollbarSize: 10,
    },
    formatOnPaste: true,
    formatOnType: true,
    suggestOnTriggerCharacters: true,
    wordWrap: 'on',
    quickSuggestions: {
      other: true,
      comments: true,
      strings: true,
    },
    suggestSelection: 'first',
    parameterHints: {
      enabled: true,
    },
  });

  return (
    <div className="component-page">
      <div className="component-header">
        <h2 className="component-title">{title}</h2>
        <div className="button-group">
          <Typography.Link onClick={handleCopy}>
            <CopyOutlined title="Copy" style={{ color: '#ff4d4f', fontSize: 20 }} />
          </Typography.Link>

          <Typography.Link onClick={handleDownload} disabled={isDownloading}>
            <DownloadOutlined
              title={isDownloading ? 'Downloading...' : 'Download'}
              style={{ color: '#52c41a', fontSize: 20 }}
            />
          </Typography.Link>
        </div>
      </div>
      <div className="component-content">
        <div className="code-container">
          <Editor
            height="100%"
            defaultLanguage="typescript"
            theme="vs-dark"
            value={displayedCode}
            options={getEditorOptions()}
            onMount={handleEditorDidMount}
            loading={<div>Loading editor...</div>}
          />
        </div>
        {error && (
          <div className="error-message">
            <strong>Error:</strong> {error}
          </div>
        )}
      </div>
    </div>
  );
};
