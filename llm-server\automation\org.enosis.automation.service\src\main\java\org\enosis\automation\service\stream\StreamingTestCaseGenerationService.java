package org.enosis.automation.service.stream;

import dev.langchain4j.rag.query.transformer.QueryTransformer;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.enosis.automation.domain.api.request.ApiRequest;
import org.enosis.automation.domain.api.response.PromptCompressionRequest;
import org.enosis.automation.domain.model.TestGeneration;
import org.enosis.automation.infrastructure.config.meter.Counted;
import org.enosis.automation.infrastructure.config.meter.Timed;
import org.enosis.automation.service.compression.CompressionService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;


@Log4j2
@Service
public class StreamingTestCaseGenerationService {
    private final CompressionService compressionService;
    private final MeterRegistry meterRegistry;
    @Value("${llm-lingua.llmlingua-2-xlm-roberta-large-meetingbank.compression.rate}")
    private Double compressionRate;

    private final Timer testCaseGenerationMethodTimer;

    public StreamingTestCaseGenerationService(CompressionService compressionService, MeterRegistry meterRegistry) {
        this.compressionService = compressionService;
        this.meterRegistry = meterRegistry;
        this.testCaseGenerationMethodTimer = Timer.builder("generateTestCase")
                .description("TestCase Generation Response Time")
                .tags("method", "generateTestCase")
                .register(meterRegistry);
    }

    @Counted(value = "generate.testcase.counter", tags = {"service", "StreamingTestCaseGenerationService"})
    public Flux<String> generateTestCase(ApiRequest request, ChatClient chatClient) {
        Instant startTime = Instant.now();
        String prompt = request.getQuestion();
        String JSON_STRUCTURE = new BeanOutputConverter<>(TestGeneration.class).getFormat();
        Mono<String> monoPrompt = request.isCompressionEnabled() ?
                compressionService.compress(PromptCompressionRequest.builder()
                                .input_prompt(prompt)
                                .rate(compressionRate)
                                .build())
                        .map(promptCompressionResponse -> {
                            log.info("Compressed Output : {} \n", promptCompressionResponse.getOutput_prompt());
                            return promptCompressionResponse.getOutput_prompt() + "\n" + JSON_STRUCTURE;
                        })
                : Mono.just(request.getQuestion() + "\n" + JSON_STRUCTURE);
        return monoPrompt
                .doOnSubscribe(subscription -> log.info("Started processing prompt"))
                .flatMapMany(data -> {
                            log.info("BEFORE GOING TO LLM : {} \n", data);
                            return chatClient.prompt(data).stream().content()
                                    .doOnNext(emitted -> {
                                        // log.info("PUSH EVENT : {} \n", emitted);
                                    });
                        }
                )
                .doOnCancel(() -> log.info("Subscriber Cancelled Streaming."))
                .doOnTerminate(() -> {
                    Instant endTime = Instant.now();
                    long processingTime = Duration.between(startTime, endTime).toSeconds();
                    log.info("Processing Time : {}", processingTime);
                    testCaseGenerationMethodTimer.record(processingTime, TimeUnit.SECONDS);
                    log.info("Processing complete");
                });
    }
}
