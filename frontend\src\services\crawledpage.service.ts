import { RestResponse } from '../types/common.types';
import { CrawledPage} from '../types/crawled-page.types';
import httpService from './http.service';

const API_ENDPOINTS = {
  CRAWLED_PAGES: '/api/crawled-pages',
  CRAWLED_PAGE: (projectId: number) => `/api/crawled-pages/${projectId}`,
  BATCH_SAVE_UPDATE: '/api/crawled-pages/batch',
  PROJECT_CRAWLED_PAGES: (id: number) => `/api/projects/${id}/crawled-pages`,
  PROJECT_CRAWLED_PAGES_SUMMARY: (id: number) => `/api/projects/${id}/crawled-pages/summary`,
  CRAWLED_PAGES_BY_IDS: '/api/crawled-pages/by-ids',
};

export const CrawledPageService = {
  /**
   * Get all crawled pages for a specific project
   */
  getByProjectId: async (projectId: number): Promise<CrawledPage[]> => {
    try {
      const response = await httpService.get<RestResponse<CrawledPage[]>>(
        'common',
        API_ENDPOINTS.PROJECT_CRAWLED_PAGES(projectId),
      );
      return response.data;
    } catch (error) {
      // Handle 404 by returning null instead of throwing
      if (error instanceof Error && error.message === 'Resource not found') {
        return [];
      }
      throw error;
    }
  },

  /**
   * Get a single crawled page by ID
   */
  getCrawledPageById: async (id: number): Promise<CrawledPage> => {
    return (
      await httpService.get<RestResponse<CrawledPage>>('common', API_ENDPOINTS.CRAWLED_PAGE(id))
    ).data;
  },

  /**
   * Create a new crawled page
   */
  createCrawledPage: async (
    crawledPage: Omit<CrawledPage, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<CrawledPage> => {
    return (
      await httpService.post<RestResponse<CrawledPage>>(
        'common',
        API_ENDPOINTS.CRAWLED_PAGES,
        crawledPage,
      )
    ).data;
  },

  /**
   * Update an existing crawled page
   */
  updateCrawledPage: async (
    id: number,
    crawledPage: Partial<CrawledPage>,
  ): Promise<CrawledPage> => {
    return (
      await httpService.put<RestResponse<CrawledPage>>(
        'common',
        API_ENDPOINTS.CRAWLED_PAGE(id),
        crawledPage,
      )
    ).data;
  },

  /**
   * Delete a crawled page
   */
  deleteCrawledPage: async (id: number): Promise<void> => {
    return httpService.delete('common', API_ENDPOINTS.CRAWLED_PAGE(id));
  },

  /**
   * Save or update a batch of crawled pages
   */
  saveOrUpdateBatch: async (crawledPages: CrawledPage[]): Promise<CrawledPage[]> => {
    return (
      await httpService.post<RestResponse<CrawledPage[]>>(
        'common',
        API_ENDPOINTS.BATCH_SAVE_UPDATE,
        { pages: crawledPages } as Record<string, unknown>,
      )
    ).data;
  },

  /**
   * Get all crawled pages summary for a specific project (without DOMJson for performance)
   */
  getByProjectIdSummary: async (projectId: number): Promise<CrawledPage[]> => {
    try {
      const response = await httpService.get<RestResponse<CrawledPage[]>>(
        'common',
        API_ENDPOINTS.PROJECT_CRAWLED_PAGES_SUMMARY(projectId),
      );
      return response.data;
    } catch (error) {
      // Handle 404 by returning empty array instead of throwing
      if (error instanceof Error && error.message === 'Resource not found') {
        return [];
      }
      throw error;
    }
  },

  /**
   * Get crawled pages by IDs with DOMJson
   */
  getByIds: async (ids: number[]): Promise<CrawledPage[]> => {
    if (!ids || ids.length === 0) {
      return [];
    }

    try {
      const response = await httpService.post<RestResponse<CrawledPage[]>>(
        'common',
        API_ENDPOINTS.CRAWLED_PAGES_BY_IDS,
        ids as unknown as Record<string, unknown>,
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching crawled pages by IDs:', error);
      return [];
    }
  },
};

export default CrawledPageService;
