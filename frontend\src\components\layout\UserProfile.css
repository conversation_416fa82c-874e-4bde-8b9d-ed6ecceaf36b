.user-profile-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.user-profile-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--secondary-color);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  font-family: 'Poppins', sans-serif;
  font-size: var(--font-regular);
}

.user-profile-button:hover {
  background-color: var(--secondary-hover);
}

.user-profile-dropdown {
  position: absolute;
  top: 50px;
  right: 0;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  min-width: 160px;
}

.user-profile-dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.user-profile-dropdown li {
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-family: 'Poppins', sans-serif;
  font-size: var(--font-regular);
}

.user-profile-dropdown li:hover {
  background-color: var(--background-light);
}

.user-profile-dropdown a {
  text-decoration: none;
  color: var(--text-primary);
  display: block;
}

.user-profile-dropdown .user-profile-logout {
  color: var(--error-color);
  font-weight: 500;
}

.user-profile-dropdown .user-profile-logout:hover {
  color: var(--error-hover);
}
