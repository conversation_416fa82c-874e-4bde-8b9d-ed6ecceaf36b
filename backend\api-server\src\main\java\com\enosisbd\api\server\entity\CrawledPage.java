package com.enosisbd.api.server.entity;

import com.enosisbd.api.server.model.CrawlOption;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.type.SqlTypes;

import java.util.Optional;

@Entity
@Table(indexes = {
    @Index(name = "idx_crawled_page_project", columnList = "project_id")
})
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class CrawledPage extends BaseEntity {
    private String pageName;
    private String pageUrl;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private CrawlOption crawlOption;

    @Type(JsonBinaryType.class)
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private Object domJson;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    public Long getProjectId() {
        return Optional.ofNullable(getProject())
                .map(Project::getId)
                .orElse(null);
    }
}
