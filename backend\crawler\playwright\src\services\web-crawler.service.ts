import { <PERSON><PERSON><PERSON>, chromium, <PERSON> } from "playwright";
import { CRAWLER_CONFIG } from "../config/crawler.config";
import {
  AuthConfig,
  CrawlOptions,
  CrawlResult,
  ElementDataShortFields,
  LoginCredentials,
} from "../types/web-crawler.types";
import logger from "../utils/logger";

const PAGE_GOTO_OPTIONS = {
  waitUntil: "networkidle",
  timeout: 120_000,
} as const;

const DEFAULT_OPTIONS: CrawlOptions = {
  skipHidden: true,
};

interface FilterConfig {
  skipTags: Set<string>;
  skipCssSelectors: string[];
  onlyTags: Set<string>;
  onlyCssSelectors: string[];
}

interface ElementAttributes {
  [key: string]: string;
}

export class WebCrawler {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private readonly headless: boolean;
  private readonly externalBrowser: boolean;

  constructor(options: { headless?: boolean; browser?: Browser } = {}) {
    this.headless = options.headless ?? true;
    this.externalBrowser = !!options.browser;
    this.browser = options.browser || null;

    if (!this.externalBrowser) {
      this.initBrowser().catch((error) => {
        logger.error("Failed to initialize browser:", error);
      });
    }
  }

  private async initBrowser(): Promise<void> {
    if (!this.browser && !this.externalBrowser) {
      try {
        this.browser = await chromium.launch({
          headless: this.headless,
          args: CRAWLER_CONFIG.browser.args,
        });
      } catch (error) {
        logger.error("Failed to initialize browser:", error);
        throw error;
      }
    }
  }

  async login(credentials: LoginCredentials): Promise<void> {
    if (!this.page) {
      throw new Error("Page is not initialized");
    }

    try {
      await this.page.goto(credentials.url, PAGE_GOTO_OPTIONS);

      if (credentials.username) {
        await this.page.fill(
          credentials.usernameSelector,
          credentials.username
        );
      }

      if (credentials.password) {
        await this.page.fill(
          credentials.passwordSelector,
          credentials.password
        );
      }

      const currentUrl = this.page.url();

      await Promise.allSettled([
        this.page.waitForURL((url) => url.toString() !== currentUrl, {
          waitUntil: "domcontentloaded",
        }),
        this.page.click(credentials.submitSelector),
      ]);

      logger.info("Login completed successfully");
    } catch (error) {
      logger.error("Login failed:", {
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw new Error(
        "Login failed: " +
          (error instanceof Error ? error.message : "Unknown error")
      );
    }
  }

  async releaseResources(): Promise<void> {
    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }

      // Only close the browser if we created it internally
      if (this.browser && !this.externalBrowser) {
        await this.browser.close();
        this.browser = null;
      }
    } catch (error) {
      logger.error("Resource cleanup failed:", error);
    }
  }

  /**
   * Detects if the current page appears to be a login page
   * @returns {Promise<boolean>} True if the page appears to be a login page
   */
  private async isLoginPage(): Promise<boolean> {
    if (!this.page) return false;

    try {
      const loginIndicators = await this.page.evaluate(() => {
        const indicators = {
          hasPasswordField: !!document.querySelector('input[type="password"]'),
          hasLoginButton: !!Array.from(
            document.querySelectorAll('button, input[type="submit"]')
          ).some((el) => {
            const text = (el.textContent || "").toLowerCase();
            const value = (
              (el instanceof HTMLInputElement && el.value) ||
              ""
            ).toLowerCase();
            return (
              text.includes("login") ||
              text.includes("sign in") ||
              text.includes("log in") ||
              value.includes("login") ||
              value.includes("sign in") ||
              value.includes("log in")
            );
          }),

          hasLoginForm: !!document.querySelector(
            'form[id*="login"], form[class*="login"], form[id*="signin"], form[class*="signin"]'
          ),

          hasLoginHeader: !!Array.from(
            document.querySelectorAll("h1, h2, h3, .header, .heading")
          ).some((el) => {
            const text = (el.textContent || "").toLowerCase();
            return (
              text.includes("login") ||
              text.includes("sign in") ||
              text.includes("log in")
            );
          }),
        };

        const indicatorCount = Object.values(indicators).filter(Boolean).length;

        return {
          indicators,
          indicatorCount,
        };
      });

      // If at least 2 indicators are present, consider it a login page
      return loginIndicators.indicatorCount >= 2;
    } catch (error) {
      logger.error("Error detecting login page:", error);
      return false;
    }
  }

  /**
   * Checks if the current URL is significantly different from the requested URL
   * which might indicate a redirect to a login page
   */
  private isLikelyRedirectedToLogin(
    requestedUrl: string,
    currentUrl: string
  ): boolean {
    try {
      if (requestedUrl === currentUrl) {
        return false;
      }

      const requestedUrlObj = new URL(requestedUrl);
      const currentUrlObj = new URL(currentUrl);

      // Different domains or subdomains indicate a likely redirect to a login system
      if (requestedUrlObj.hostname !== currentUrlObj.hostname) {
        return true;
      }

      // Check for common auth-related paths
      const authPaths = [
        "/login",
        "/signin",
        "/auth",
        "/authorize",
        "/authentication",
      ];
      if (
        authPaths.some((path) =>
          currentUrlObj.pathname.toLowerCase().includes(path)
        )
      ) {
        return true;
      }

      // Different paths with auth params might indicate a redirect
      if (
        requestedUrlObj.pathname !== currentUrlObj.pathname &&
        (currentUrlObj.searchParams.has("redirect") ||
          currentUrlObj.searchParams.has("return") ||
          currentUrlObj.searchParams.has("returnUrl"))
      ) {
        return true;
      }

      return false;
    } catch (error) {
      logger.error("Error comparing URLs:", error);
      return false;
    }
  }

  async crawl(
    url: string,
    pageName?: string,
    authConfig?: AuthConfig,
    crawlOptions?: CrawlOptions
  ): Promise<CrawlResult> {
    if (!this.browser && !this.externalBrowser) {
      await this.initBrowser();
    }

    if (!this.browser) {
      throw new Error("Browser initialization failed");
    }

    const options = { ...DEFAULT_OPTIONS, ...crawlOptions };

    const browser = this.browser;
    this.page = await browser.newPage({
      bypassCSP: true,
      ignoreHTTPSErrors: true,
    });

    // Optimize by disabling unnecessary resources
    await this.page.route(
      "**/*.{png,jpg,jpeg,gif,svg,woff,woff2,ttf,otf,eot}",
      (route) => route.abort()
    );
    await this.page.route(
      "**/*(?:analytics|advertisement|ads|tracker|tracking)*.js",
      (route) => route.abort()
    );

    try {
      if(crawlOptions?.needsLogin) {
        
          // Set authentication before any navigation
        if (authConfig) {
          // Set cookies if provided
          if (authConfig.cookies && authConfig.cookies.length > 0) {
            await this.page.context().addCookies(authConfig.cookies);
          }

          // Set local storage if provided
          if (authConfig.localStorage && authConfig.localStorage.length > 0) {
            await this.page.goto(url, PAGE_GOTO_OPTIONS);
            for (const item of authConfig.localStorage) {
              await this.page.evaluate(
                ({ key, value }: { key: string; value: string }) => {
                  localStorage.setItem(key, value);
                },
                item
              );
            }
          }

          if (authConfig.bearerToken) {
            await this.page.setExtraHTTPHeaders({
              Authorization: `Bearer ${authConfig.bearerToken}`,
            });
          }

          if (authConfig.credentials) {
            await this.login(authConfig.credentials);
          }
        }
      }
      
        // If we've already navigated for localStorage setup, refresh the page, otherwise do the initial navigation
        if (authConfig?.localStorage && authConfig.localStorage.length > 0) {
          await this.page.reload(PAGE_GOTO_OPTIONS);
        } else {
          await this.page.goto(url, PAGE_GOTO_OPTIONS);
        }

        // Get the current URL after navigation
        const currentUrl = this.page.url();
        if(currentUrl != url) {
          logger.warn(
                  "Authentication failed: Still on login page after applying credentials",
                  {
                    requestedUrl: url,
                    currentUrl
                  }
                );
      
          throw new Error(
            "Authentication failed: The provided credentials were not accepted or were insufficient."
          );
        }

      // Use a single JS context to process all elements - this is much more efficient
      const { elements, logs } = await this.page.evaluate(
        (options: CrawlOptions) => {
          const logs: string[] = [];
          const log = (...messages: unknown[]) =>
            logs.push(messages.map(String).join(" "));

          // Function to get filter configuration
          const getFilterConfig = (options: CrawlOptions): FilterConfig => {
            const skipTags = new Set([
              "img",
              "svg",
              "picture",
              "figure",
              "script",
              "meta",
              "link",
              "style",
              "noscript",
              "iframe",
              "br",
              "hr",
              "wbr",
              "path",
            ]);

            options.skipTags?.forEach((tag) => skipTags.add(tag.toLowerCase()));

            // Create CSS selector regex patterns for skipping
            const skipCssSelectors: string[] = [];
            if (options.skipHidden !== false) {
              skipCssSelectors.push(
                "[hidden]",
                "[type='hidden']",
                "[style*='display: none']",
                "[style*='visibility: hidden']",
                "[style*='opacity: 0']"
              );
            }

            // Add user-defined CSS selectors to skip
            options.skipCssSelectors?.forEach((selector) =>
              skipCssSelectors.push(selector)
            );

            // Only include these tags if specified
            const onlyTags = new Set(
              options.onlyTags?.map((tag: string) => tag.toLowerCase())
            );

            // Only include these CSS selectors if specified
            const onlyCssSelectors = options.onlyCssSelectors ?? [];

            return { skipTags, skipCssSelectors, onlyTags, onlyCssSelectors };
          };

          const elementMatchesAnyCssSelectors = (
            element: Element,
            cssSelectors: string[]
          ): boolean => {
            if (!cssSelectors.length) return false;
            return cssSelectors.some((selector) => element.matches(selector));
          };

          const shouldSkipElement = (
            element: Element,
            filterConfig: FilterConfig
          ): boolean => {
            const { skipTags, skipCssSelectors, onlyTags, onlyCssSelectors } =
              filterConfig;
            const tagName = element.tagName.toLowerCase();

            if (skipTags.has(tagName)) {
              return true;
            }

            if (elementMatchesAnyCssSelectors(element, skipCssSelectors)) {
              return true;
            }

            if (onlyTags.size > 0 && !onlyTags.has(tagName)) {
              return true;
            }

            if (
              onlyCssSelectors.length > 0 &&
              !elementMatchesAnyCssSelectors(element, onlyCssSelectors)
            ) {
              return true;
            }

            if (options.skipHidden !== false) {
              const isVisible = element.checkVisibility();
              return !isVisible;
            }

            return false;
          };

          const isGenericElement = (elementData: {
            tag: string;
            text: string;
            xPath: string;
          }): boolean => {
            const { tag, text, xPath } = elementData;

            // Generic body with no attributes
            if (
              xPath.startsWith("/html/html/body") &&
              (text?.trim()?.length ?? 0) === 0
            ) {
              return true;
            }

            if (tag === "div" && !text && xPath.split("/").length <= 3) {
              return true;
            }

            if (tag === "span" && !text) {
              return true;
            }

            return false;
          };

          const truncateText = (text: string | null | undefined): string => {
            if (!text) return "";
            if (text.length <= 40) return text;

            const cleanedText = text.replace(/[\n\t\s]+|&nbsp;/g, " ");

            return cleanedText.length <= 40
              ? cleanedText
              : `${cleanedText.slice(0, 40).trim()}*`;
          };

          const getElementTextContent = (
            element: Element,
            isElementInteractive: boolean
          ): string => {
            // First check for direct text nodes (highest priority)
            let directText = "";
            for (const node of Array.from(element.childNodes)) {
              if (node.nodeType === Node.TEXT_NODE && node.textContent) {
                directText += node.textContent;
              }
            }

            const trimmedDirectText = directText.trim();
            if (trimmedDirectText) {
              return trimmedDirectText;
            }

            // Then check for explicit text attributes if no direct text
            const ariaLabel = element.getAttribute("aria-label")?.trim();
            const title = element.getAttribute("title")?.trim();

            if (ariaLabel) return ariaLabel;
            if (title) return title;

            // Last resort: use the full textContent (includes child text)
            // This will only be used for interactive elements like buttons with only child elements providing text
            // Only use full textContent for interactive elements to avoid duplication
            if (isElementInteractive) {
              const fullText = element.textContent?.trim() || "";
              return fullText;
            }

            return "";
          };

          const getElementAttributes = (
            element: Element
          ): ElementAttributes => {
            const attrs: ElementAttributes = {};
            for (const attr of Array.from(element.attributes)) {
              attrs[attr.name] = attr.value;
            }
            return attrs;
          };

          const isXPathUnique = (xpath: string): boolean => {
            const elements = document.evaluate(
              xpath,
              document,
              null,
              XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
              null
            );
            return elements.snapshotLength === 1;
          };

          const getUniqueAttributeXPath = (element: Element): string | null => {
            // Priority attributes that could identify an element uniquely
            const priorityAttributes = [
              "id",
              "name",
              "data-testid",
              "aria-label",
              "title",
              "class",
            ];

            for (const attr of priorityAttributes) {
              const value = element.getAttribute(attr);
              if (value) {
                // Handle class names separately as they can be multiple
                if (attr === "class") {
                  const classes = value.trim().split(/\s+/);
                  for (const className of classes) {
                    const xpath = `//*[contains(@class, "${className}")]`;
                    if (isXPathUnique(xpath)) {
                      return xpath;
                    }
                  }
                } else {
                  const xpath = `//*[@${attr}="${value}"]`;
                  if (isXPathUnique(xpath)) {
                    return xpath;
                  }
                }
              }
            }
            return null;
          };

          const getElementXPath = (element: Element): string => {
            if (element.id) {
              return `//*[@id="${element.id}"]`;
            }

            // Try unique attribute-based XPath
            const attrXPath = getUniqueAttributeXPath(element);
            if (attrXPath) {
              return attrXPath;
            }

            // Build paths from bottom up, trying to find shortest unique path
            let current: Element | null = element;
            let path = "";
            let tagName = element.tagName.toLowerCase();

            // Try just the tag name if it's unique
            let xpath = `//${tagName}`;
            if (isXPathUnique(xpath)) {
              return xpath;
            }

            while (current && current.nodeType === Node.ELEMENT_NODE) {
              let sibling = current.previousSibling;
              let index = 1;
              while (sibling) {
                if (
                  sibling.nodeType === Node.ELEMENT_NODE &&
                  (sibling as Element).tagName === current.tagName
                ) {
                  index++;
                }
                sibling = sibling.previousSibling;
              }

              const currentPath = `/${current.tagName.toLowerCase()}${
                index > 1 ? `[${index}]` : ""
              }`;
              path = currentPath + path;

              // Check if the current path is unique
              xpath = `//${path.substring(1)}`;
              if (isXPathUnique(xpath)) {
                return xpath;
              }

              current = current.parentElement;
            }

            // If we haven't found a unique path yet, return the full path
            return `/html${path}`;
          };

          const isRedundantElement = (
            element: Element,
            attributes: ElementAttributes
          ): boolean => {
            const tag = element.tagName.toLowerCase();

            // Check for redundant layout containers or wrappers
            const layoutTags = ["div", "section", "article"];
            if (layoutTags.includes(tag)) {
              // Helper function to check for direct text nodes
              const hasDirectText = (): boolean => {
                for (const node of element.childNodes) {
                  if (
                    node.nodeType === Node.TEXT_NODE &&
                    node.textContent?.trim()
                  ) {
                    return true;
                  }
                }
                return false;
              };

              // Helper to check for important attributes
              const hasImportantAttributes =
                attributes.id ||
                attributes["data-testid"] ||
                attributes["aria-label"] ||
                attributes.name ||
                attributes.role;

              // Check if it's a layout container (flex/grid)
              const layoutClasses = ["flex", "grid", "row", "col", "container"];
              const hasLayoutClass =
                attributes.class &&
                layoutClasses.some((cls) => attributes.class.includes(cls));

              if (hasLayoutClass) {
                // Check if it has more than one interactive child
                const interactiveSelectors = [
                  "button",
                  "a",
                  "input",
                  "select",
                  "textarea",
                  '[role="button"]',
                  "[tabindex]",
                  '[role="link"]',
                ];
                const interactiveChildren = element.querySelectorAll(
                  interactiveSelectors.join(",")
                );

                // If it has 0 or 1 interactive child and no direct text, it's redundant
                if (interactiveChildren.length <= 1 && !hasDirectText()) {
                  return true;
                }
              }

              // Check if it's a simple wrapper with just one child
              if (element.children.length === 1 && !hasImportantAttributes) {
                return true;
              }

              // Check if it's a wrapper with no direct text but with text in children
              if (
                !hasDirectText() &&
                element.children.length > 0 &&
                element.textContent?.trim() &&
                !hasImportantAttributes
              ) {
                return true;
              }
            }

            return false;
          };

          const processElement = (
            element: Element,
            filterConfig: FilterConfig
          ): { tag: string; xPath: string; text: string } | null => {
            try {
              const tag = element.tagName.toLowerCase();
              const attributes = getElementAttributes(element);

              // Check if element is interactive
              const interactiveTags = [
                "a",
                "button",
                "input",
                "select",
                "textarea",
                "details",
              ];
              const hasInteractiveRole =
                attributes.role === "button" ||
                attributes.role === "link" ||
                attributes.tabindex !== undefined;
              const isInteractive =
                interactiveTags.includes(tag) || hasInteractiveRole;

              // Skip meaningless elements early
              if (
                tag === "span" &&
                !getElementTextContent(element, isInteractive)
              ) {
                return null;
              }
              const text = truncateText(
                getElementTextContent(element, isInteractive)
              );

              // Skip if element is not interactive or meaningful
              if (
                !isInteractive &&
                !text &&
                !attributes.id &&
                !attributes["data-testid"]
              ) {
                return null;
              }

              // Check if element is redundant
              if (isRedundantElement(element, attributes)) {
                return null;
              }

              const xPath = getElementXPath(element);

              // Create the element data object
              const elementData = { tag, xPath, text };

              // Check if element is generic
              if (isGenericElement(elementData)) {
                return null;
              }

              return elementData;
            } catch (error) {
              log("Error processing element:", error);
              return null;
            }
          };

          // Main processing function using TreeWalker for efficient DOM traversal
          const processDOM = (): ElementDataShortFields[] => {
            const filterConfig = getFilterConfig(options);
            const elements: ElementDataShortFields[] = [];
            const processedXPaths = new Set<string>();

            // Create a TreeWalker to traverse the DOM
            const walker = document.createTreeWalker(
              document.body,
              NodeFilter.SHOW_ELEMENT,
              {
                acceptNode: function (element: Element) {
                  // Quick pre-filter to improve performance
                  const skip = shouldSkipElement(element, filterConfig);
                  if (skip) {
                    return NodeFilter.FILTER_SKIP; // Skip this node
                  }
                  return NodeFilter.FILTER_ACCEPT;
                },
              }
            );

            // Process elements in batches for better performance
            const batchSize = 100;
            let batchElements: Element[] = [];
            let currentNode = walker.nextNode();

            while (currentNode) {
              batchElements.push(currentNode as Element);

              // Process batch when it reaches the size limit
              if (batchElements.length >= batchSize) {
                processBatch(
                  batchElements,
                  filterConfig,
                  elements,
                  processedXPaths
                );
                batchElements = [];
              }

              currentNode = walker.nextNode();
            }

            // Process any remaining elements
            if (batchElements.length > 0) {
              processBatch(
                batchElements,
                filterConfig,
                elements,
                processedXPaths
              );
            }

            return elements;
          };

          // Function to process a batch of elements
          const processBatch = (
            elements: Element[],
            filterConfig: FilterConfig,
            results: ElementDataShortFields[],
            processedXPaths: Set<string>
          ): void => {
            for (const element of elements) {
              const elementData = processElement(element, filterConfig);
              if (elementData && !processedXPaths.has(elementData.xPath)) {
                processedXPaths.add(elementData.xPath);

                // Compress field names (tx → text, xp → xPath, etc.)
                const compressedData: ElementDataShortFields = {
                  t: elementData.tag,
                  x: elementData.xPath,
                  tx: elementData.text,
                };

                results.push(compressedData);
              }
            }
          };

          const elements = processDOM();

          return { elements, logs };
        },
        options
      );

      logger.debug("Page Evaluate logs:", { logs });
      logger.debug("Found elements:", { count: elements.length });

      return { url: url, pageName: pageName == null ? "" : pageName, el: elements };
    } catch (error) {
      logger.error("Error during crawling:", error);
      throw error;
    } finally {
      // Don't clean up if using external browser, just close the page
      if (this.externalBrowser) {
        if (this.page) {
          await this.page.close();
          this.page = null;
        }
      } else {
        await this.releaseResources();
      }
    }
  }

  async close(): Promise<void> {
    await this.releaseResources();
  }

  async destroy(): Promise<void> {
    await this.releaseResources();
  }
}
