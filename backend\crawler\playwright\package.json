{"name": "playwright-crawler", "version": "1.0.0", "description": "Web crawler using Playwright", "main": "dist/app.js", "scripts": {"start": "node --max-old-space-size=8192 dist/app.js", "dev:playwright": "ts-node-dev --respawn src/app.ts", "build": "tsc", "test": "npx mocha --require ts-node/register 'temp/**/*.ts' --timeout 60000", "test:ts": "npx mocha --require ts-node/register 'src/**/*.test.ts' --timeout 60000"}, "dependencies": {"axios": "^1.7.9", "chromedriver": "^135.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.2", "morgan": "^1.10.0", "selenium-webdriver": "^4.29.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "@playwright/test": "^1.40.0", "playwright": "^1.40.0", "uuid": "^9.0.0"}, "devDependencies": {"@playwright/test": "^1.50.1", "@types/chai": "^4.3.20", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/mocha": "^10.0.10", "@types/morgan": "^1.9.9", "@types/node": "^20.11.16", "@types/selenium-webdriver": "^4.1.21", "@types/uuid": "^9.0.0", "chai": "^4.5.0", "mocha": "^10.8.2", "playwright": "^1.50.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3"}}