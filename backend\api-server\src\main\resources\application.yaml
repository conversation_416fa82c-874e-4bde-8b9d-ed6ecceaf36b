spring:
  application:
    name: api-server
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  datasource:
    url: ${SPRING_DATASOURCE_URL:************************************}
    username: ${SPRING_DATASOURCE_USERNAME:postgres}
    password: ${SPRING_DATASOURCE_PASSWORD:root}
    driver-class-name: org.postgresql.Driver
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: ${SPRING_JPA_HIBERNATE_DDL_AUTO:validate}
  liquibase:
    change-log: classpath:db/master.xml
    enabled: true
    contexts: dev
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${app.google-oauth2.client-id}
            client-secret: ${app.google-oauth2.client-secret}
            redirect-uri: "${app.google-oauth2.url}/api/auth/oauth2/google/callback"
            scope: email,profile,https://www.googleapis.com/auth/spreadsheets.readonly

server:
  port: 8085

springdoc:
  swagger-ui:
    path: /swagger-ui/index.html

# Enhanced Log Configuration
logging:
  # File configuration
  file:
    name: logs/${spring.application.name}-error.log
  logback:
    rollingpolicy:
      file-name-pattern: logs/${spring.application.name}-error-%d{yyyy-MM-dd}.log
      max-history: 10
      clean-history-on-start: true

  # Enhanced patterns
  pattern:
    # Detailed console pattern with colored output, IP and request tracking
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%-5level) [%clr(%thread){magenta}] %clr(%-40.40logger{39}){cyan} [IP: %X{clientIp:-UNKNOWN}] [ReqID: %X{requestId:-SYSTEM}] : %msg%n"
    # Comprehensive file pattern for error investigation
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{40} [IP: %X{clientIp:-UNKNOWN}] [ReqID: %X{requestId:-SYSTEM}] : %msg%n"

  # Log level configuration
  level:
    # Root logger - show everything in console
    root: INFO

    # Application-specific logging
    com.enosisbd.api.server:
      console: INFO
      file: ERROR

    # Framework logging optimization
    org.springframework:
      console: WARN
      file: ERROR
    org.hibernate:
      console: WARN
      file: ERROR
    org.apache:
      console: WARN
      file: ERROR

# Security Rotation Configuration
app:
  security:
    key-rotation:
      enabled: true
      interval-hours: 24
      key-keep-count: 3

  # Google Sheets Configuration
  google-sheets:
    credentials-file: ${GOOGLE_SHEETS_CREDENTIALS_FILE:classpath:credentials.json}
    use-service-account: ${GOOGLE_SHEETS_USE_SERVICE_ACCOUNT:true}
    # Reuse OAuth configuration from spring.security.oauth2.client.registration.google
    reuse-oauth-config: ${GOOGLE_SHEETS_REUSE_OAUTH_CONFIG:true}
