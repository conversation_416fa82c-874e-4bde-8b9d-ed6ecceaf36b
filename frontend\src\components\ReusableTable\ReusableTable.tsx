import { flexRender } from '@tanstack/react-table';
import { Pagination } from 'antd';
import React from 'react';
import './ReusableTable.css';

// SortIndicator component that shows the sorting direction
const SortIndicator = ({ isSorted }) => {
  if (!isSorted) {
    return <span className="sort-indicator">↕</span>;
  }
  return <span className="sort-indicator">{isSorted === 'asc' ? '↑' : '↓'}</span>;
};

interface ReusableTableProps {
  table: any;
  onRowClick?: (e: React.MouseEvent, id: string) => void;
  onAdd?: () => void;
  addButtonText?: string;
  defaultPageSize?: number;
}

const ReusableTable = ({
  table,
  onRowClick,
  onAdd,
  addButtonText = 'Add New',
  defaultPageSize = 5,
}: ReusableTableProps) => {
  const [currentPage, setCurrentPage] = React.useState(1);
  const [pageSize, setPageSize] = React.useState(defaultPageSize);

  const handleClick = (e: React.MouseEvent, id: string) => {
    if (onRowClick) {
      onRowClick(e, id);
    }
  };

  // Calculate pagination
  const totalItems = table.getRowModel().rows.length;
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalItems);
  const currentRows = table.getRowModel().rows.slice(startIndex, endIndex);

  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
  };

  return (
    <div className="table-container">
      {onAdd && (
        <div className="table-header">
          <button type="button" className="btn-black" onClick={onAdd}>
            {addButtonText}
          </button>
        </div>
      )}
      <table className="reusable-table">
        <thead>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className={header.column.getCanSort() ? 'sortable-header' : ''}
                  onClick={header.column.getToggleSortingHandler()}
                >
                  <div className="header-content">
                    {flexRender(header.column.columnDef.header, header.getContext())}
                    {header.column.getCanSort() && (
                      <SortIndicator isSorted={header.column.getIsSorted()} />
                    )}
                  </div>
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {currentRows.map((row) => (
            <tr key={row.id} onClick={(e) => handleClick(e, row.original.id)}>
              {row.getVisibleCells().map((cell) => (
                <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
      <div className="table-footer">
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={totalItems}
          onChange={handlePageChange}
          showSizeChanger
          showQuickJumper={false}
          pageSizeOptions={['5', '10', '20', '50', '100']}
        />
      </div>
    </div>
  );
};

export default ReusableTable;
