import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../ContextApi/useAuth';
import './UserProfile.css';

interface UserProfileProps {
  logout: () => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ logout }) => {
  const { user } = useAuth();

  const [menuOpen, setMenuOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = (event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      setMenuOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleMenuItemClick = () => {
    setMenuOpen(false);
  };

  const handleLogout = () => {
    setMenuOpen(false);
    logout();
  };

  return (
    <div className="user-profile-container" ref={dropdownRef}>
      <button
        className="user-profile-button"
        aria-label="User profile"
        onClick={() => setMenuOpen(!menuOpen)}
      >
        <b>{String(user?.id).slice(0, 3).toLocaleUpperCase()}</b>
      </button>

      {menuOpen && (
        <div className="user-profile-dropdown">
          <ul>
            <li>
              <Link to="/profile" onClick={handleMenuItemClick}>
                Profile
              </Link>
            </li>
            <li className="user-profile-logout" onClick={handleLogout}>
              Logout
            </li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default UserProfile;
