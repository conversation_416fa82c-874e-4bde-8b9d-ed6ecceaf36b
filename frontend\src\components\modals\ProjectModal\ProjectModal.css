/* ProjectModal specific responsive styles */
.project-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
  padding: 10px;
}

.project-modal-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  width: 100%;
  max-width: 600px;
  max-height: calc(100vh - 30px);
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.project-modal-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.modal-form-row {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.modal-form-group.half {
  flex: 1;
}

.modal-form-group.third {
  flex: 1;
}

.modal-divider {
  margin: 10px 0;
  border: 0;
  border-top: 1px solid #e8e8e8;
}

/* Compact form styles */
.project-modal-container .modal-form-group {
  margin-bottom: 10px;
}

.project-modal-container .modal-form-group label {
  margin-bottom: 4px;
  font-size: 13px;
  font-weight: 500;
}

/* Style for select dropdown */
.modal-form-group select {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: white;
  font-size: 13px;
  height: 32px;
}

/* Style for number input */
.modal-form-group input[type='number'] {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 13px;
  height: 32px;
}

/* Style for text inputs */
.project-modal-container .modal-form-group input[type='text'],
.project-modal-container .modal-form-group input:not([type]) {
  padding: 6px 8px;
  font-size: 13px;
  height: 32px;
}

/* Style for textarea */
.project-modal-container .modal-form-group textarea {
  padding: 6px 8px;
  font-size: 13px;
  min-height: 60px;
}

/* Loading overlay styles */
.project-modal-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  border-radius: var(--border-radius-lg);
}

.project-modal-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.project-modal-loading-content span {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.button-with-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Compact section headers */
.project-modal-container h3 {
  font-size: 14px;
  font-weight: 600;
  margin: 12px 0 8px 0;
  color: var(--text-primary);
}

/* Compact form padding */
.project-modal-container .padding-lg {
  padding: 12px 16px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .project-modal-overlay {
    padding: 10px;
  }

  .project-modal-container {
    max-width: 100%;
    max-height: calc(100vh - 20px);
  }

  .modal-form-row {
    flex-direction: column;
    gap: 10px;
  }

  .modal-form-group.half,
  .modal-form-group.third {
    flex: none;
  }
}

/* For very small screens, keep three-column layout but with smaller gaps */
@media (min-width: 481px) and (max-width: 600px) {
  .modal-form-row {
    gap: 6px;
  }

  .modal-form-group.third {
    min-width: 0;
  }

  .modal-form-group.third label {
    font-size: 12px;
  }

  .modal-form-group.third select,
  .modal-form-group.third input {
    font-size: 12px;
    padding: 4px 6px;
    height: 28px;
  }
}

@media (max-width: 480px) {
  .project-modal-overlay {
    padding: 5px;
  }

  .project-modal-container {
    max-height: calc(100vh - 10px);
  }
}
