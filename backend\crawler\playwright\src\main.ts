import { WebCrawler } from './services/web-crawler.service';
import type { CrawlResult } from './types/web-crawler.types';

// Test code for the WebCrawler class
async function testWebCrawler() {
  let crawler: WebCrawler | null = null;
  try {
    crawler = new WebCrawler({ headless: true });
    
    console.log('Starting crawl of example.com...');
    const result: CrawlResult = await crawler.crawl('http://localhost:3000/');
    
    // Dump complete results in a structured format
    console.log('\n=== COMPLETE CRAWL RESULTS ===');
    console.log(JSON.stringify(result, null, 2));

    // Print detailed element analysis
    console.log('\n=== ELEMENT ANALYSIS ===');
    
    // Elements with selectors
    console.log('\nElements with selectors:');
    result.el.forEach((el, index) => {
      console.log(`\n[Element ${index + 1}]`);
      console.log(`Tag: ${el.t}`);
    });

    // Statistics
    console.log('\n=== STATISTICS ===');
    console.log(`Total elements: ${result.el.length}`);
    
    // Element types breakdown
    const tagCounts = new Map<string, number>();
    result.el.forEach(el => {
      tagCounts.set(el.t, (tagCounts.get(el.t) || 0) + 1);
    });
    
    console.log('\nElement types breakdown:');
    Array.from(tagCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .forEach(([tag, count]) => {
        console.log(`${tag}: ${count}`);
      });
    
  } catch (error) {
    console.error('Error during crawl:', error);
  } finally {
    if (crawler) {
      await crawler.destroy();
      crawler = null;
    }
  }
}


testWebCrawler().catch(console.error);
