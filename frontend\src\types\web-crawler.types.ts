export type SelectorStrategy =
  | "data-testid"
  | "id"
  | "name"
  | "class"
  | "class+parent"
  | "other";

export type ElementSelector = {
  type: SelectorStrategy;
  cssSelector: string;
};

export type ElementData = {
  tag: string;
  xPath: string;
  text?: string;
  attributes?: Record<string, string>;
  selector?: ElementSelector;
  interactions?: string[];
  contextScreenshot?: string; // base64 encoded surrounding context
};

export type ElementDataWithoutTag = Omit<ElementData, "tag">;

export type ElementDataShortFields = {
  t: string;
  x: string;
  tx?: string;
};

export type CrawlResult = {
  url: string;
  pageName: string;
  el: ElementDataShortFields[];
};

export type LoginCredentials = {
  url: string;
  username?: string;
  password?: string;
  usernameSelector: string;
  passwordSelector: string;
  submitSelector: string;
};

export type Cookie = {
  name: string;
  value: string;
  url?: string;
  domain?: string;
  path?: string;
  expires?: number;
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: "Strict" | "Lax" | "None";
};

export type AuthConfig = {
  cookies?: Cookie[];
  localStorage?: { key: string; value: string }[];
  bearerToken?: string;
  credentials?: LoginCredentials;
  [key: string]: any;
};

export type CrawlRequest = {
  url: string;
  pageName: string;
  auth?: AuthConfig;
  options?: CrawlOptions;
};

export type CrawlOptions = {
  skipTags?: string[];
  skipCssSelectors?: string[];
  skipHidden?: boolean;
  onlyTags?: string[];
  onlyCssSelectors?: string[];
  needsLogin?: boolean;
};
