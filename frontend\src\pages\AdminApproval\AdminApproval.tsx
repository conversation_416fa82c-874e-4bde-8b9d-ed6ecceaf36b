import { ReloadOutlined } from '@ant-design/icons';
import { Table, Typography, notification, Select } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import ApprovalService from '../../services/approval.service';
import { User } from '../../types/user.types';
import './AdminApproval.css';
import { format, parseISO } from 'date-fns';

const AdminApprovalPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [updatingIds, setUpdatingIds] = useState<Set<number>>(new Set());
  const [refreshTrigger, setRefreshTrigger] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [showRefreshNotification, setShowRefreshNotification] = useState<boolean>(false);
  const [approvalFilter, setApprovalFilter] = useState<'all' | 'approved' | 'unapproved'>('all');

  const showNotification = (message: string, type: 'success' | 'warning' | 'error') => {
    notification[type]({
      message,
      placement: 'bottomRight',
      duration: 3,
      style: {
        minWidth: '240px',
        maxWidth: '320px',
        minHeight: '40px',
        padding: '12px',
      },
      className: 'custom-small-notification',
    });
  };

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    try {
      const data = await ApprovalService.getAllUsers();
      setUsers(data);
      if (showRefreshNotification) {
        showNotification('Data refreshed.', 'success');
      }
    } catch (error) {
      showNotification('Failed to load users', 'error');
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
      setShowRefreshNotification(false);
    }
  }, [showRefreshNotification]);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers, refreshTrigger]);

  const handleApprovalChange = async (userId: number, newApprovedState: boolean) => {
    const user = users.find((u) => u.id === userId);
    if (!user) return;

    setUsers((prev) =>
      prev.map((u) => (u.id === userId ? { ...u, approved: newApprovedState } : u)),
    );

    setUpdatingIds((prev) => {
      const newSet = new Set(prev);
      newSet.add(userId);
      return newSet;
    });

    try {
      await ApprovalService.updateApproval(user.email, newApprovedState);
      showNotification(
        `${user.email} has been ${newApprovedState ? 'activated' : 'deactivated'}.`,
        newApprovedState ? 'success' : 'warning',
      );
      setRefreshTrigger((prev) => !prev);
    } catch (error) {
      setUsers((prev) =>
        prev.map((u) => (u.id === userId ? { ...u, approved: !newApprovedState } : u)),
      );
      showNotification('Failed to update status', 'error');
    } finally {
      setUpdatingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(userId);
        return newSet;
      });
    }
  };

  const normalizedSearch = searchTerm.trim().toLowerCase();

  const filteredUsers = users.filter((user) => {
    // Filter by search term
    const matchesSearch =
      user.fullName?.toLowerCase().includes(normalizedSearch) ||
      user.email.toLowerCase().includes(normalizedSearch);

    // Filter by approval status
    const matchesApprovalFilter =
      approvalFilter === 'all' ||
      (approvalFilter === 'approved' && user.approved) ||
      (approvalFilter === 'unapproved' && !user.approved);

    return matchesSearch && matchesApprovalFilter;
  });

  return (
    <div>
      <div className="dashboard-header">
        <h2 className="dashboardHeaderPadding">Users</h2>
        <div className="search-filter-container">
          <div className="search-container">
            <input
              type="text"
              className="search-input"
              placeholder="Search user by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <span className="search-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </span>
          </div>

          <Select
            className="approval-filter-select"
            value={approvalFilter}
            onChange={(value) => setApprovalFilter(value as 'all' | 'approved' | 'unapproved')}
            options={[
              { value: 'all', label: 'All Users' },
              { value: 'approved', label: 'Active Users' },
              { value: 'unapproved', label: 'Inactive Users' }
            ]}
          />
          <Typography.Link
            onClick={() => {
              setShowRefreshNotification(true);
              setRefreshTrigger((prev) => !prev);
            }}
            disabled={loading}
          >
            <ReloadOutlined title={'Reload'} style={{ color: '#ff4d4f', fontSize: 20 }} />
          </Typography.Link>
        </div>
      </div>
      <div className="admin-approval-content">
        <div className="table-wrapper">
          <Table
            size="small"
            rowKey="id"
            columns={[
              {
                title: 'Full Name',
                dataIndex: 'fullName',
                key: 'fullName',
                render: (text: string | null) => text || 'N/A',
              },
              {
                title: 'Email',
                dataIndex: 'email',
                key: 'email',
              },
              {
                title: 'Created At',
                dataIndex: 'createdAt',
                key: 'createdAt',
                render: (text: string | null) => {
                  if (!text) return 'N/A';

                  const date = parseISO(text);
                  const month = format(date, 'MMM');
                  const dateNum = format(date, 'do');
                  const year = format(date, 'yyyy');
                  const time = format(date, 'h:mm a');

                  return `${month} ${dateNum}, ${year}, ${time}`;
                },
              },
              {
                title: 'Status',
                dataIndex: 'approved',
                key: 'approved',
                align: 'center',
                render: (_: any, record: User) => {
                  const isUpdating = updatingIds.has(record.id);
                  const isApproved = record.approved;

                  return (
                    <div className="approval-switch">
                      <div className="switch-container">
                        <button
                          className={`approve-btn ${isApproved ? 'active' : ''} ${
                            isUpdating ? 'loading' : ''
                          }`}
                          disabled={isUpdating || isApproved}
                          onClick={() => handleApprovalChange(record.id, true)}
                        >
                          {isUpdating && isApproved ? (
                            <span className="dual-ring-spinner"></span>
                          ) : (
                            'Active'
                          )}
                        </button>
                        <div className="switch-divider"></div>
                        <button
                          className={`deny-btn ${!isApproved ? 'active' : ''} ${
                            isUpdating ? 'loading' : ''
                          }`}
                          disabled={isUpdating || !isApproved}
                          onClick={() => handleApprovalChange(record.id, false)}
                        >
                          {isUpdating && !isApproved ? (
                            <span className="dual-ring-spinner"></span>
                          ) : (
                            'Inactive'
                          )}
                        </button>
                      </div>
                    </div>
                  );
                },
              },
            ]}
            dataSource={filteredUsers}
            pagination={{ pageSize: 5 }}
            loading={loading}
            locale={{
              emptyText: updatingIds.size > 0 ? 'Updating users...' : 'No users found',
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default AdminApprovalPage;
