package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.UserPreference;
import io.hypersistence.utils.spring.repository.BaseJpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserPreferenceRepository extends BaseJpaRepository<UserPreference, Long> {
    Optional<UserPreference> findByUserId(Long userId);
}
