FROM maven:3.9.6-eclipse-temurin-17 AS builder

WORKDIR /build
COPY . .
RUN mvn clean package -DskipTests

FROM codercom/code-server:latest

USER root
RUN apt-get update && \
    apt-get install -y openjdk-17-jre && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

ENV JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
ENV PATH="${JAVA_HOME}/bin:${PATH}"

# Create the environment directory
RUN mkdir -p /usr/local/environment

# Copy all scripts and fix line endings
COPY scripts/setup_user_repo.sh /tmp/setup_user_repo.sh
COPY scripts/destroy_user_repo.sh /tmp/destroy_user_repo.sh
COPY scripts/delete_repo.sh /tmp/delete_repo.sh
COPY scripts/clean_repos.sh /tmp/clean_repos.sh

# Convert Windows line endings (CRLF) to Unix line endings (LF)
RUN sed -i 's/\r$//' /tmp/setup_user_repo.sh && \
    sed -i 's/\r$//' /tmp/destroy_user_repo.sh && \
    sed -i 's/\r$//' /tmp/delete_repo.sh && \
    sed -i 's/\r$//' /tmp/clean_repos.sh && \
    mv /tmp/setup_user_repo.sh /usr/local/environment/ && \
    mv /tmp/destroy_user_repo.sh /usr/local/environment/ && \
    mv /tmp/delete_repo.sh /usr/local/environment/ && \
    mv /tmp/clean_repos.sh /usr/local/environment/

# Create the start script directly in the container
RUN echo '#!/bin/bash' > /usr/local/environment/start.sh && \
    echo 'mkdir -p /home/<USER>/.config/code-server' >> /usr/local/environment/start.sh && \
    echo 'mkdir -p /home/<USER>/projects' >> /usr/local/environment/start.sh && \
    echo 'if [ ! -f /home/<USER>/.config/code-server/config.yaml ]; then' >> /usr/local/environment/start.sh && \
    echo '  echo "bind-addr: 0.0.0.0:8080\nauth: none\npassword: \ncert: false" > /home/<USER>/.config/code-server/config.yaml' >> /usr/local/environment/start.sh && \
    echo 'fi' >> /usr/local/environment/start.sh && \
    echo 'chmod -R 777 /home/<USER>/.config' >> /usr/local/environment/start.sh && \
    echo 'chmod -R 777 /home/<USER>/projects' >> /usr/local/environment/start.sh && \
    echo 'code-server --auth none --bind-addr 0.0.0.0:8080 /home/<USER>/projects &' >> /usr/local/environment/start.sh && \
    echo 'java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005 -jar /usr/local/container-controller.jar' >> /usr/local/environment/start.sh

# Set permissions
RUN chmod -R 755 /usr/local/ && \
    chmod +x /usr/local/environment/*.sh && \
    mkdir -p /home/<USER>/.config/code-server && \
    mkdir -p /home/<USER>/projects && \
    chown -R coder:coder /home/<USER>/.config && \
    chown -R coder:coder /home/<USER>/projects && \
    chmod -R 777 /home/<USER>/.config && \
    chmod -R 777 /home/<USER>/projects

# Copy the JAR file
COPY --from=builder /build/target/*.jar /usr/local/container-controller.jar

# Create a default config file
RUN echo "bind-addr: 0.0.0.0:8080\nauth: none\npassword: \ncert: false" > /home/<USER>/.config/code-server/config.yaml && \
    chown coder:coder /home/<USER>/.config/code-server/config.yaml

USER coder
EXPOSE 8080 8081

ENTRYPOINT ["/bin/bash", "/usr/local/environment/start.sh"]