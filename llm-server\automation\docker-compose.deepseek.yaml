services:
  ollama_server:
    container_name: ollama-server
    image: ollama/ollama:0.5.11
    restart: always
    environment:
      OLLAMA_NUM_PARALLEL: "3"
      OLLAMA_DEBUG: "1"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [ gpu ]
    volumes:
      - ./ollama-storage:/root/.ollama
    ports:
      - "11434:11434"
    networks:
      ollama-network:
        ipv4_address: **************


# Network subnet is needed to prevent clash from the gateway of server ip
networks:
  ollama-network:
    driver: bridge
    ipam:
      config:
        - subnet: *************/24
