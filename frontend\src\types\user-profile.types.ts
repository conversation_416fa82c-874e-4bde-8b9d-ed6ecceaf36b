export interface UserProfile {
  id: number;
  fullName: string;
  email: string;
  [key: string]: unknown;
}

export interface PasswordChange {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  [key: string]: unknown;
}

export interface UserPreference {
  id?: number;
  entityViewMode: EntityViewMode;
  [key: string]: unknown;
}

export interface PasswordChangeResponse {
  message: string;
}

export type EntityViewMode = 'card' | 'table';
