package com.enosisbd.api.server.dto;

import com.enosisbd.api.server.model.EntityType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * DTO for representing a node in a tree structure of entities
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TreeNodeDto {
    private Long id;
    private String name;
    private EntityType type;
    
    @Builder.Default
    private List<TreeNodeDto> children = new ArrayList<>();
}
