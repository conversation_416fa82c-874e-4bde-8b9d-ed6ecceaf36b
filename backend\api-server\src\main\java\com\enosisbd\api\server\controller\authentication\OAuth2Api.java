package com.enosisbd.api.server.controller.authentication;

import com.enosisbd.api.server.dto.AuthenticationDTO.SignInResponseDTO;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface OAuth2Api {
    @GetMapping("/google/callback")
    ResponseEntity<Void> googleCallback(@RequestParam(required = false) String code, 
                                       @RequestParam(required = false) String error);

    @GetMapping("/oauth/callback")
    ResponseEntity<SignInResponseDTO> authCallBack(@RequestParam String code);

    @GetMapping("/google")
    ResponseEntity<Void> googleLogin();
}
