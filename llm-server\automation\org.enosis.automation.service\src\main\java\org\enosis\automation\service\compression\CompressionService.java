package org.enosis.automation.service.compression;

import lombok.RequiredArgsConstructor;
import org.enosis.automation.domain.api.response.PromptCompressionRequest;
import org.enosis.automation.domain.api.response.PromptCompressionResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service @RequiredArgsConstructor
public class CompressionService {

    private final WebClient webClient;
    private final RestClient restClient;

    public Mono<PromptCompressionResponse> compress(PromptCompressionRequest request) {
        return webClient.post()
                .uri("/prompt/compress")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(PromptCompressionResponse.class);
    }

    public ResponseEntity<PromptCompressionResponse> compressBlocking(PromptCompressionRequest request) {
        return restClient.post()
                .uri("/prompt/compress")
                .body(request)
                .retrieve()
                .toEntity(PromptCompressionResponse.class);
    }
}
