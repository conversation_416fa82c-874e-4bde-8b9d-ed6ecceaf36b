services:
  # PostgreSQL Database
  postgres:
    image: postgres:16
    container_name: postgres
    environment:
      POSTGRES_DB: tap
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: root
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - app_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d tap"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Spring Boot Backend
  backend:
    build:
      context: ./backend/api-server
      dockerfile: Dockerfile
    image: api-server
    container_name: backend
    environment:
      SPRING_DATASOURCE_URL: ***********************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: root
      SPRING_JPA_HIBERNATE_DDL_AUTO: validate
      SPRING_PROFILES_ACTIVE: dev
    ports:
      - "8085:8085"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - app_network

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=development
    image: frontend
    container_name: frontend
    ports:
      - "5173:5173"
    depends_on:
      - backend
    networks:
      - app_network

  # Playwright Crawler
  playwright-crawler:
    build:
      context: ./backend/crawler/playwright
      dockerfile: Dockerfile
    image: playwright-crawler
    container_name: playwright-crawler
    ports:
      - "9099:9099"
    depends_on:
      - backend
    volumes:
      # - ./backend/crawler/playwright/logs:/app/logs
      - playwright_logs:/app/logs
    networks:
      - app_network

  custom-code-server:
    build:
      context: ./code-server
      dockerfile: Dockerfile
    image: custom-code-server
    container_name: custom-code-server
    expose:
      - "8080:8080"
    ports:
      - "8081:8081"
      - "5005:5005"
    environment:
      - SPRING_PROFILE_ACTIVE=dev
    volumes:
      - projects_volume:/home/<USER>/projects
      - config_volume:/home/<USER>/.config/code-server
    networks:
      - app_network

  oauth2-proxy-github:
    container_name: oauth2-proxy-github
    image: quay.io/oauth2-proxy/oauth2-proxy:latest
    environment:
      - OAUTH2_PROXY_CLIENT_ID=Ov23liP5nv7mIG7cl93n
      - OAUTH2_PROXY_CLIENT_SECRET=2b539ee5306af7941917711cf7967de0e56d7205
      - OAUTH2_PROXY_COOKIE_SECRET=YXVHZnFZRit2WVJndTFNQXZDcnJHV1BUVlYwWTkrbnM=
      - OAUTH2_PROXY_PROVIDER=github
      - OAUTH2_PROXY_EMAIL_DOMAINS=*
      - OAUTH2_PROXY_REVERSE_PROXY=true
      - OAUTH2_PROXY_REDIRECT_URL=http://localhost:8082/oauth2/callback
      - OAUTH2_PROXY_COOKIE_SECURE=false
      - OAUTH2_PROXY_UPSTREAM=http://custom-code-server:8080
      - OAUTH2_PROXY_UPSTREAMS=http://custom-code-server:8080/
      - OAUTH2_PROXY_HTTP_ADDRESS=http://0.0.0.0:4180
      - OAUTH2_PROXY_SET_AUTHORIZATION_HEADER=true
      - OAUTH2_PROXY_SET_XAUTHREQUEST=true
      - OAUTH2_PROXY_WHITELIST_DOMAINS=*
      - OAUTH2_PROXY_SKIP_PROVIDER_BUTTON=true
      - OAUTH2_PROXY_PASS_HOST_HEADER=true
      - OAUTH2_PROXY_PASS_ACCESS_TOKEN=true
    ports:
      - "8082:4180"
    depends_on:
      - custom-code-server
    networks:
      - app_network

  oauth2-proxy-bitbucket:
    container_name: oauth2-proxy-bitbucket
    image: quay.io/oauth2-proxy/oauth2-proxy:latest
    environment:
      - OAUTH2_PROXY_CLIENT_ID=mYWBQqzfFs3ayFFSvp
      - OAUTH2_PROXY_CLIENT_SECRET=fEEpHLgrGxeLPpKKzLmmvwMXBFSCFWth
      - OAUTH2_PROXY_COOKIE_SECRET=YXVHZnFZRit2WVJndTFNQXZDcnJHV1BUVlYwWTkrbnM=
      - OAUTH2_PROXY_PROVIDER=bitbucket
      - OAUTH2_PROXY_EMAIL_DOMAINS=*
      - OAUTH2_PROXY_REVERSE_PROXY=true
      - OAUTH2_PROXY_REDIRECT_URL=http://localhost:8083/oauth2/callback
      - OAUTH2_PROXY_COOKIE_SECURE=false
      - OAUTH2_PROXY_UPSTREAM=http://custom-code-server:8080
      - OAUTH2_PROXY_UPSTREAMS=http://custom-code-server:8080/
      - OAUTH2_PROXY_HTTP_ADDRESS=http://0.0.0.0:4180
      - OAUTH2_PROXY_SET_AUTHORIZATION_HEADER=true
      - OAUTH2_PROXY_SET_XAUTHREQUEST=true
      - OAUTH2_PROXY_WHITELIST_DOMAINS=*
      - OAUTH2_PROXY_SKIP_PROVIDER_BUTTON=true
      - OAUTH2_PROXY_PASS_HOST_HEADER=true
      - OAUTH2_PROXY_PASS_ACCESS_TOKEN=true
    ports:
      - "8083:4180"
    depends_on:
      - custom-code-server
    networks:
      - app_network


networks:
  app_network:
    driver: bridge

volumes:
  postgres_data:
  playwright_logs:
  projects_volume:
    driver: local
  config_volume:
    driver: local
