import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { setRequestContext, clearRequestContext } from '../utils/logger';

export const requestContextMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const requestId = uuidv4();
  const clientIp = getClientIP(req);
  
  setRequestContext(requestId, clientIp);
  
  res.on('finish', () => {
    clearRequestContext();
  });
  
  next();
};

function getClientIP(req: Request): string {
  const forwardedFor = req.headers['x-forwarded-for'];
  if (forwardedFor) {
    return Array.isArray(forwardedFor) 
      ? forwardedFor[0] 
      : forwardedFor.split(',')[0].trim();
  }
  return req.socket.remoteAddress || 'UNKNOWN';
}