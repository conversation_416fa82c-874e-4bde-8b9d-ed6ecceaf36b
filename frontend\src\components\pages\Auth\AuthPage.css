.auth-container {
  max-width: 460px;
  margin: 52px auto;
  padding: 1.8rem;
  text-align: center;
}

.auth-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #1f1f1f;
}

.auth-subtitle {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 1.5rem;
}

.auth-error {
  color: #ff4d4f;
  margin-top: 8px;
  font-size: 14px;
  font-weight: 500;
}

.auth-form {
  text-align: left;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 13px;
  font-weight: 500;
  color: #232323;
}

.form-group input {
  width: 100%;
  padding: 6px 10px 6px 40px;
  border: 1px solid #bdc4d3;
  border-radius: var(--border-radius-lg);
  font-size: var(--font-regular);
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  color: #3f4043;
  z-index: 1;
}

.forgot-password {
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
  color: var(--primary-color);
  cursor: pointer;
  margin-bottom: 1rem;
}

.auth-button {
  margin-top: 0.5rem;
  margin-bottom: 1.5rem;
  width: 100%;
  border-radius: var(--border-radius-lg);
  height: 36px;
}

.auth-footer {
  display: block;
  text-align: center;
  font-size: 14px;
}

.auth-link {
  font-weight: 500;
  font-size: 14px;
  color: var(--primary-color) !important;
}

.alert {
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.alert-success {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #389e0d;
}

.alert-error {
  background-color: #fff1f0;
  border: 1px solid #ffa39e;
  color: #cf1322;
}

.oauth-callback-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  margin-top: -20vh; /* Shifts content 20% above center */
}

.social-auth-button {
  width: 100%;
  margin-bottom: 20px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #bdc4d3;
  border-radius: var(--border-radius-lg);
  background-color: transparent;
  color: rgba(0, 0, 0, 0.85);
  box-shadow: none;
  font-weight: 500;
  font-size: 14px;
}

.social-auth-button:hover {
  background-color: #f3d3d3;
  color: var(--color-red-600);
  border: none;
}

.social-auth-button .anticon {
  margin-right: 8px;
}

.google-button {
  position: relative;
  text-align: center;
}

.google-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  vertical-align: middle;
}

.divider-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  width: 100%;
}

.divider-line {
  flex-grow: 1;
  height: 1px;
  background-color: var(--border-color, #e0e0e0);
  border: none;
}

.divider-text {
  padding: 0 10px;
  color: var(--text-secondary, #666);
  font-size: 13px;
}
