import {
  AuthConfig,
  CrawlOptions,
  CrawlResult,
} from '../types/web-crawler.types';
import httpService from './http.service';

const API_ENDPOINTS = {
  CRAWLED_DOM_JSON: '/api/scraper/scrape',
};

export const CrawlerService = {
  /**
   * Get all projects
   */
  getCrawledDOMJson: async (url: string, pageName: string, auth: AuthConfig, options: CrawlOptions): Promise<CrawlResult> => {
    return httpService.post<CrawlResult>('crawler', API_ENDPOINTS.CRAWLED_DOM_JSON, { url, pageName, auth, options });
  },
};
