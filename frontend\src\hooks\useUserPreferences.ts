import React, { useContext } from 'react';
import { AuthContext } from '../components/ContextApi/AuthContextInstance';
import { EntityViewMode, UserPreference } from '../types/user-profile.types';

/**
 * Custom hook to access and update user preferences
 * @returns Object containing user preferences and methods to update them
 */
export const useUserPreferences = () => {
  const auth = useContext(AuthContext);

  if (!auth) {
    throw new Error('useUserPreferences must be used within an AuthProvider');
  }

  const { userPreferences, refreshUserPreferences, updateUserPreferences } = auth;

  /**
   * Get the current view mode preference
   * @returns The current view mode ('card' or 'table')
   */
  const getViewMode = React.useCallback((): EntityViewMode => {
    return userPreferences?.entityViewMode || 'card';
  }, [userPreferences]);

  /**
   * Update the view mode preference
   * @param viewMode The new view mode ('card' or 'table')
   * @returns Promise that resolves to the updated preferences
   */
  const setViewMode = React.useCallback(
    async (viewMode: EntityViewMode ): Promise<UserPreference> => {
      if (!userPreferences) {
        // If preferences don't exist yet, fetch them first
        const prefs = await refreshUserPreferences();
        return updateUserPreferences({ ...prefs, entityViewMode: viewMode });
      }
      return updateUserPreferences({ ...userPreferences, entityViewMode: viewMode });
    },
    [userPreferences, refreshUserPreferences, updateUserPreferences],
  );

  return {
    preferences: userPreferences,
    getViewMode,
    setViewMode,
    refreshPreferences: refreshUserPreferences,
  };
};

export default useUserPreferences;
