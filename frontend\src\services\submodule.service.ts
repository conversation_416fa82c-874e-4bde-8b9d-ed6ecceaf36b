import { SheetTestCasesResponse } from '../types/automate-process.types';
import { RestResponse } from '../types/common.types';
import httpService from './http.service';

const API_ENDPOINTS = {
  GENERATE_TEST_CASES: (submoduleId: number) => `/api/submodules/${submoduleId}/test-cases`,
};

/**
 * Service for test case generation
 */
const SubModuleService = {
  /**
   * Generate test cases for a submodule from Google Sheets
   * @param submoduleId The submodule ID
   * @returns The generated test cases
   */
  generateTestCases: async (submoduleId: number): Promise<SheetTestCasesResponse> => {
    const response = await httpService.get<RestResponse<SheetTestCasesResponse>>(
      'common',
      API_ENDPOINTS.GENERATE_TEST_CASES(submoduleId),
    );
    return response.data;
  },
};

export default SubModuleService;
