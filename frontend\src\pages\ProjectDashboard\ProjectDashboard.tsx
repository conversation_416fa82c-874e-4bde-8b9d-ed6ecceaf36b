import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Spin, message } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ActionButtons from '../../components/ActionButtons/ActionButtons';
import CardView from '../../components/Card/CardView';
import ConfirmModal from '../../components/modals/common/ConfirmModal';
import { ProjectModal } from '../../components/modals/ProjectModal/ProjectModal';
import { ShareModal } from '../../components/modals/ShareModal/ShareModal';
import ReusableTable from '../../components/ReusableTable/ReusableTable';
import { ViewMode, ViewToggle } from '../../components/ViewToggle/ViewToggle';
import useUserPreferences from '../../hooks/useUserPreferences';
import ProjectService from '../../services/project.service';
import UserService from '../../services/user.service';
import { Project, ProjectFormData } from '../../types/project.types';
import { SharedUser } from '../../types/sharing.types';

// Add index signature to Project if it doesn't have one, to satisfy BaseItem
interface DashboardProject extends Project {
  [key: string]: unknown; // Index signature
}

export const ProjectDashboard = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<DashboardProject[]>([]); // Use DashboardProject
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('card');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProject, setSelectedProject] = useState<DashboardProject | null>(null); // Use DashboardProject
  const [sharedUsers, setSharedUsers] = useState<SharedUser[]>([]);

  const columnHelper = useMemo(() => createColumnHelper<DashboardProject>(), []); // Use DashboardProject

  const loadProjects = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      // Assuming ProjectService returns Project[], cast to DashboardProject[]
      const data: Project[] = await ProjectService.getAllProjects();
      setProjects(data as DashboardProject[]);
    } catch (err) {
      setError('Failed to load projects. Please try again.');
      console.error('Error loading projects:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Use the preferences hook instead of making API calls
  const { getViewMode } = useUserPreferences();

  useEffect(() => {
    setViewMode(getViewMode());
  }, [getViewMode]); // Include getViewMode in the dependency array

  useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  const filteredProjects = useMemo(() => {
    if (!searchQuery.trim()) return projects;

    const query = searchQuery.toLowerCase();
    return projects.filter(
      (project) =>
        project.name?.toLowerCase().includes(query) || // Optional chaining
        project.description?.toLowerCase().includes(query), // Optional chaining
    );
  }, [projects, searchQuery]);

  // Handler for CardView clicks
  const handleCardClick = useCallback(
    (e: React.MouseEvent, itemData: DashboardProject['id'] | DashboardProject) => {
      if ((e.target as HTMLElement)?.closest('.item-actions')) {
        return;
      }
      const projectId = typeof itemData === 'object' ? itemData.id : itemData;
      navigate(`/project/${projectId}/script-generation`);
    },
    [navigate],
  );

  // Handler for Table Row clicks
  const handleTableRowClick = useCallback(
    (e: React.MouseEvent, projectId: string) => {
      if ((e.target as HTMLElement)?.closest('.item-actions')) {
        return;
      }
      navigate(`/project/${projectId}/script-generation`);
    },
    [navigate],
  );

  const handleAddProject = useCallback(() => {
    setSelectedProject(null);
    setIsModalOpen(true);
  }, []);

  // Edit handler needs DashboardProject type
  const handleEditProject = useCallback((project: DashboardProject, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedProject(project);
    setIsModalOpen(true);
  }, []);

  // Delete confirm handler needs DashboardProject type
  const handleDeleteConfirm = useCallback(
    async (project: DashboardProject) => {
      if (!project) return;

      try {
        setIsLoading(true);
        setError(null);
        await ProjectService.deleteProject(project.id);
        setProjects((prev) => prev.filter((p) => p.id !== project.id));
      } catch (err) {
        setError('Failed to delete project. Please try again.');
        console.error('Error deleting project:', err);
      } finally {
        setIsLoading(false);
      }
    },
    [setIsLoading, setError],
  );

  // Delete click handler needs DashboardProject type
  const handleDeleteProject = useCallback(
    (project: DashboardProject, e: React.MouseEvent) => {
      e.stopPropagation();
      ConfirmModal.delete({
        itemName: project.name,
        itemType: 'project',
        onOk: () => handleDeleteConfirm(project),
        isLoading,
      });
    },
    [handleDeleteConfirm, isLoading],
  );

  // Share project handler
  const handleShareProject = useCallback(async (project: DashboardProject, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedProject(project);
    try {
      const users = await UserService.getSharedUsers('project', project.id);
      setSharedUsers(users);
      setIsShareModalOpen(true);
    } catch (error) {
      console.error('Error fetching shared users:', error);
      message.error('Failed to load shared users');
    }
  }, []);

  // Add user to shared list
  const handleAddSharedUser = useCallback(
    async (userId: number) => {
      if (!selectedProject) return;

      await UserService.shareEntity('project', selectedProject.id, userId);
      // Refresh the list of shared users
      const users = await UserService.getSharedUsers('project', selectedProject.id);
      setSharedUsers(users);
    },
    [selectedProject],
  );

  // Remove user from shared list
  const handleRemoveSharedUser = useCallback(
    async (userId: number) => {
      if (!selectedProject) return;

      await UserService.removeEntityAccess('project', selectedProject.id, userId);
      // Refresh the list of shared users
      const users = await UserService.getSharedUsers('project', selectedProject.id);
      setSharedUsers(users);
    },
    [selectedProject],
  );

  // Save handler needs DashboardProject type for state updates
  const handleSaveProject = useCallback(
    async (data: ProjectFormData) => {
      try {
        setIsLoading(true);
        setError(null);

        const saveData = {
          name: data.name,
          description: data.description,
          googleSheetUrl: data.googleSheetUrl || undefined,
          submoduleColumn: data.submoduleColumn || undefined,
          submoduleStartRow: data.submoduleStartRow || undefined,
          caseIdColumn: data.caseIdColumn || undefined,
          testCaseDescriptionColumn: data.testCaseDescriptionColumn || undefined,
          testCaseExpectedResultColumn: data.testCaseExpectedResultColumn || undefined,
        };

        let savedProject: Project;
        if (selectedProject) {
          // Update the project
          savedProject = await ProjectService.updateProject(selectedProject.id, saveData);

          // Update state correctly mapping to DashboardProject
          const now = new Date().toISOString();
          setProjects((prev) =>
            prev.map((p) =>
              p.id === savedProject.id
                ? ({ ...savedProject, updatedAt: now } as DashboardProject)
                : p,
            ),
          );
        } else {
          savedProject = await ProjectService.createProject(saveData);
          // Update state correctly adding DashboardProject
          setProjects((prev) => [...prev, { ...savedProject } as DashboardProject]);
        }
        setIsModalOpen(false);
        setSelectedProject(null); // Clear selection
      } catch (err) {
        setError(`Failed to ${selectedProject ? 'update' : 'create'} project. Please try again.`);
        console.error('Error saving project:', err);
      } finally {
        setIsLoading(false);
      }
    },
    [selectedProject],
  );

  // Columns definition uses DashboardProject
  const columns = useMemo(
    () => [
      columnHelper.accessor('name', {
        header: 'Title',
        cell: (info) => <div className="title-cell">{info.getValue()}</div>,
      }),
      columnHelper.accessor('description', {
        header: 'Description',
        cell: (info) => <div className="description-cell">{info.getValue()}</div>,
      }),
      columnHelper.accessor('createdAt', {
        header: 'Created',
        cell: (info) => (
          <div className="regular-cell">
            {/* Add check for valid date */}
            {info.getValue()
              ? new Date(info.getValue() as string).toLocaleDateString('en-US', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric',
                })
              : 'N/A'}
          </div>
        ),
      }),
      columnHelper.accessor('updatedAt', {
        header: 'Last Updated',
        cell: (info) => (
          <div className="regular-cell">
            {/* Add check for valid date */}
            {info.getValue()
              ? new Date(info.getValue() as string).toLocaleDateString('en-US', {
                  day: '2-digit',
                  month: 'short',
                  year: 'numeric',
                })
              : 'N/A'}
          </div>
        ),
      }),
      columnHelper.display({
        id: 'actions',
        header: 'Actions',
        cell: (info) => (
          // Ensure ActionButtons type matches if generic
          <ActionButtons<DashboardProject>
            entity={info.row.original}
            entityType="Project"
            entityNameProperty="name" // Pass keyof T directly
            onEdit={handleEditProject}
            onDelete={handleDeleteProject}
            onShare={handleShareProject}
            isDirectlyShared={info.row.original.isDirectlyShared !== false}
          />
        ),
      }),
    ],
    [columnHelper, handleEditProject, handleDeleteProject, handleShareProject],
  );

  const table = useReactTable({
    data: filteredProjects,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  // Update CardView usage
  const renderCardView = useCallback(
    () => (
      <CardView<DashboardProject> // Specify type for CardView
        items={filteredProjects}
        onItemClick={handleCardClick} // Use card-specific handler
        onAddClick={handleAddProject}
        addButtonText="Add New Project"
        titleProperty="name"
        descriptionProperty="description"
        metaItems={[
          {
            label: 'Created',
            // Update value function signature
            value: (project) =>
              project.createdAt
                ? new Date(project.createdAt).toLocaleDateString('en-US', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                  })
                : 'N/A',
            className: 'meta-created', // Keep a distinct class if needed, or use default
          },
          {
            label: 'Updated',
            // Update value function signature
            value: (project) =>
              project.updatedAt
                ? new Date(project.updatedAt).toLocaleDateString('en-US', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric',
                  })
                : 'N/A',
            // IMPORTANT: Assign the className used by CardView to align actions
            className: 'meta-date',
          },
        ]}
        actionButtons={{
          entityType: 'Project',
          entityNameProperty: 'name',
          onEdit: handleEditProject,
          onDelete: handleDeleteProject,
          onShare: handleShareProject,
          getIsDirectlyShared: (project: DashboardProject) => project.isDirectlyShared !== false,
        }}
        showDescriptionTooltip={true}
      />
    ),
    [
      filteredProjects,
      handleCardClick,
      handleAddProject,
      handleEditProject,
      handleDeleteProject,
      handleShareProject,
    ],
  );

  // Update TableView usage
  const renderTableView = useCallback(
    () => (
      <ReusableTable
        table={table}
        onRowClick={handleTableRowClick} // Use table-specific handler
        onAdd={handleAddProject}
        addButtonText="Add New Project"
      />
    ),
    [table, handleTableRowClick, handleAddProject], // Correct dependency
  );

  // Return statement remains largely the same
  return (
    <div>
      <div className="dashboard-header">
        <h2 className="dashboardHeaderPadding">Projects</h2>
        <div className="search-container">
          <input
            type="text"
            className="search-input"
            placeholder="Search projects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <span className="search-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </span>
        </div>
        <div className="dashboard-actions">
          <ViewToggle viewMode={viewMode} onViewModeChange={setViewMode} />
        </div>
      </div>
      <div className="dashboard-content">
        {isLoading && projects.length === 0 ? (
          <div className="loading">
            <Spin size="large" />
          </div>
        ) : error ? (
          <div className="error-message">{error}</div>
        ) : (
          <>
            {filteredProjects?.length === 0 ? (
              <div className="empty-state">
                {searchQuery ? (
                  <div>
                    <h2>No Projects Found</h2>
                    <p>
                      No projects found matching "{searchQuery}". Try a different search term or
                      create a new project.
                    </p>
                  </div>
                ) : (
                  <div>
                    <h2>No Projects Found</h2>
                    <p>Create your first project to get started</p>
                    <button type="button" className="btn-primary" onClick={handleAddProject}>
                      Add Project
                    </button>
                  </div>
                )}
              </div>
            ) : viewMode === 'card' ? (
              renderCardView()
            ) : (
              renderTableView()
            )}
          </>
        )}
      </div>

      {/* Project Modal */}
      <ProjectModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedProject(null);
        }}
        onSave={handleSaveProject}
        initialData={
          selectedProject
            ? {
                name: selectedProject.name,
                description: selectedProject.description,
                googleSheetUrl: selectedProject.googleSheetUrl,
                submoduleColumn: selectedProject.submoduleColumn,
                submoduleStartRow: selectedProject.submoduleStartRow,
                caseIdColumn: selectedProject.caseIdColumn,
                testCaseDescriptionColumn: selectedProject.testCaseDescriptionColumn,
                testCaseExpectedResultColumn: selectedProject.testCaseExpectedResultColumn,
              }
            : undefined
        }
        title={selectedProject ? 'Edit Project' : 'Add New Project'}
        isLoading={isLoading}
      />

      {/* Share Modal */}
      {selectedProject && (
        <ShareModal
          isOpen={isShareModalOpen}
          onClose={() => {
            setIsShareModalOpen(false);
            setSelectedProject(null);
            setSharedUsers([]);
          }}
          entityType="Project"
          entity={selectedProject}
          sharedUsers={sharedUsers}
          onAddUser={handleAddSharedUser}
          onRemoveUser={handleRemoveSharedUser}
        />
      )}
    </div>
  );
};
