import { exec } from "child_process";
import { Router } from "express";
import fs from "fs/promises";
import path from "path";
import logger from "../utils/logger";

const router = Router();
const TEMP_CODE_DIR = path.join(__dirname, "..", "..", "tests");

interface ExecuteCodeRequest {
  code: string;
  projectId: string;
  versionId: string;
  testSuiteId: string;
  platform: "Selenium" | "Playwright";
  language: "TypeScript";
}

// Ensure temp directory exists
async function ensureTempDir() {
  try {
    await fs.access(TEMP_CODE_DIR);
  } catch {
    await fs.mkdir(TEMP_CODE_DIR, { recursive: true });
  }
}

// Execute the test script code
router.post("/code/execute", async (req, res) => {
  try {
    const { code, projectId, versionId, testSuiteId, platform, language } = req.body as ExecuteCodeRequest;
    const timestamp = Date.now();
    const tempFile = path.join(TEMP_CODE_DIR, `file_${timestamp}_${projectId}_${versionId}_${testSuiteId}.test.ts`);

     // If code, projectId, versionId, and testSuiteId is provided in the request, save it to the temp file
     if (code && projectId && versionId && testSuiteId) {
      await ensureTempDir();
      await fs.writeFile(tempFile, code, "utf-8");
    } else {
      return res.status(400).json({
        success: false,
        error: "Code, project ID, version ID, and test suite ID are required",
      });
    }

    // Check if file exists
    try {
      await fs.access(tempFile);
    } catch {
      return res.status(404).json({
        success: false,
        error: "No code file found to execute",
      });
    }
    
    // Execute based on platform and language
    try {
      let result;
      if (platform == "Selenium" && language == "TypeScript") {
        result = await executeTest(tempFile, "selenium");
      } else if (platform == "Playwright" && language == "TypeScript") {
        result = await executeTest(tempFile, "playwright");
      } else {
        throw new Error(`Unsupported platform (${platform}) or language (${language})`);
      }
      
      res.json(result);
    } catch (error) {
      logger.error("Error executing code:", error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred while executing code",
      });
    } finally {
      // Delete the temp file after execution
      try {
        await fs.unlink(tempFile);
      } catch (err) {
        logger.warn("Failed to delete temporary test file:", err);
      }
    }
  } catch (error) {
    logger.error("Error executing code:", error);
    res.status(500).json({
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Unknown error occurred while executing code",
    });
  }
});

// Helper function to execute tests
function executeTest(tempFile: string, type: "selenium" | "playwright"): Promise<any> {
  return new Promise((resolve) => {
    const command = type === "selenium" 
      ? `npx mocha --require ts-node/register "${tempFile}" --timeout 30000`
      : `npx playwright test`;
      
    exec(
      command,
      {
        cwd: path.dirname(tempFile),
        env: {
          ...process.env,
          TS_NODE_PROJECT: path.join(__dirname, "..", "..", "tsconfig.json"),
          TS_NODE_TRANSPILE_ONLY: "true",
          ...(type === "playwright" ? { PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "1" } : {}),
        },
      },
      (error, stdout, stderr) => {
        if (error && !stdout) {
          logger.error(`Error executing ${type} test:`, error);
          resolve({
            success: false,
            error: error.message,
            output: stderr || "Execution failed",
          });
        } else {
          resolve({
            success: true,
            output: stdout || stderr,
            error: stderr || null,
          });
        }
      }
    );
  });
}

export default router;
