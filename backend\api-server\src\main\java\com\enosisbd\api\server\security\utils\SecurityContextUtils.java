package com.enosisbd.api.server.security.utils;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
public class SecurityContextUtils {

    /**
     * Retrieves the authentication object for the currently logged-in user.
     * The authentication contains user details, credentials, and granted authorities.
     *
     * @return Authentication object representing the current security context's principal,
     *         or null if no authentication is available
     * @see org.springframework.security.core.Authentication
     * @see org.springframework.security.core.context.SecurityContextHolder
     */
    public static Authentication getCurrentUser() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

}
