package com.enosisbd.api.server.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for Google Sheets integration
 */
@Configuration
@ConfigurationProperties(prefix = "app.google-sheets")
@Getter
@Setter
public class GoogleSheetsProperties {

    /**
     * Path to the credentials file for service account authentication (only used if useServiceAccount is true)
     */
    private String credentialsFile = "classpath:credentials.json";

    /**
     * Whether to use service account authentication (true) or user OAuth (false)
     */
    private boolean useServiceAccount = true;

    /**
     * Whether to check if the user has access to the sheet before processing
     */
    private boolean checkAccess = false;
}
