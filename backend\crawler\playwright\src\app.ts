import cors from "cors";
import dotenv from "dotenv";
import express from "express";
import morgan from "morgan";
import path from "path";
import apiRouter from "./api";
import { requestContextMiddleware } from "./middleware/request-context.middleware";
import logger from "./utils/logger";

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, "../.env") });

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(requestContextMiddleware);
app.use(morgan("dev")); // HTTP request logger

// Routes
app.use("/api", apiRouter);

app.use(
  (
    err: Error,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    logger.error("Unhandled error:", { error: err.message, stack: err.stack });
    res
      .status(500)
      .json({ error: "Internal Server Error", message: err.message });
  }
);

// 404 handler
app.use((req: express.Request, res: express.Response) => {
  logger.warn("Route not found:", { method: req.method, path: req.path });
  res
    .status(404)
    .json({ error: "Not Found", message: `Cannot ${req.method} ${req.path}` });
});

const PORT = process.env.PORT || 9099;

app.listen(PORT, () => logger.info(`Server running on port ${PORT}`));
