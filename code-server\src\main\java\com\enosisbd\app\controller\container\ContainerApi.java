package com.enosisbd.app.controller.container;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface ContainerApi {
  @GetMapping("/container/start")
  ResponseEntity<String> start(@AuthenticationPrincipal OAuth2User oauth2User,
      @RequestParam(required = false) String gitRepositoryUrl,
      @RequestParam(required = false) String gitUserEmail,
      HttpServletRequest request);

  @GetMapping("/container/stop")
  ResponseEntity<String> stop(@RequestParam String username,
      @RequestParam String repository);
}
