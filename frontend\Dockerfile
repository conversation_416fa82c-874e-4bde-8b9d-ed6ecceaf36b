# --- Build Stage ---
FROM node:22-alpine as build
WORKDIR /frontend

COPY package*.json ./
RUN npm install

COPY . .

# Set build arguments and environment
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
# Explicitly copy the appropriate .env file
RUN if [ "$NODE_ENV" = "production" ]; then \
      cp .env.production .env; \
      rm -f .env.development; \
    else \
      cp .env.development .env; \
      rm -f .env.production; \
    fi


RUN npm run build

# --- Runtime Stage ---
FROM nginx:alpine
COPY --from=build /frontend/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 5173
CMD ["nginx", "-g", "daemon off;"]
