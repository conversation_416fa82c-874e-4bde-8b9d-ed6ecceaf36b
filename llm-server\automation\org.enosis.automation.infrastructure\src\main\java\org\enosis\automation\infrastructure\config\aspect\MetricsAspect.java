package org.enosis.automation.infrastructure.config.aspect;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.enosis.automation.infrastructure.config.meter.Counted;
import org.enosis.automation.infrastructure.config.meter.Timed;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
public class MetricsAspect {

    private final MeterRegistry meterRegistry;

    public MetricsAspect(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    @Around("@annotation(timed)")
    public Object timeMethod(ProceedingJoinPoint joinPoint, Timed timed) throws Throwable {
        String metricName = timed.value().isEmpty()
                ? joinPoint.getSignature().getName()
                : timed.value();

        Timer timer = Timer.builder(metricName)
                .tags(timed.tags())
                .description("Timing of method execution")
                .register(meterRegistry);

        Instant startTime = Instant.now();
        try {
            return joinPoint.proceed();
        } finally {
            Instant endTime = Instant.now();
            timer.record(Duration.between(startTime, endTime));
        }
    }

    @Around("@annotation(counted)")
    public Object countMethod(ProceedingJoinPoint joinPoint, Counted counted) throws Throwable {
        String metricName = counted.value().isEmpty()
                ? joinPoint.getSignature().getName()
                : counted.value();

        Counter counter = Counter.builder(metricName)
                .tags(counted.tags())
                .description("Count of method invocations")
                .register(meterRegistry);

        counter.increment();
        return joinPoint.proceed();
    }
}