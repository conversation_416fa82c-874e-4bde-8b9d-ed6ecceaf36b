package com.enosisbd.api.server.dto;

import com.enosisbd.api.server.model.CrawlOption;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
public class CrawledPageDto extends BaseDto {
    private String pageName;
    private String pageUrl;
    private CrawlOption crawlOption;
    private Object domJson;
    @NotNull(message = "Project ID cannot be null")
    private Long projectId;
}
