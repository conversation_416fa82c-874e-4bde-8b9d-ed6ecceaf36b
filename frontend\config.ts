// Base URLs
export const CRAWLER_BASE_URL = import.meta.env.VITE_CRAWLER_BASE_URL || 'http://localhost:9099';
export const COMMON_BASE_URL = import.meta.env.VITE_COMMON_BASE_URL || 'http://localhost:8085';
export const LLM_BASE_URL = import.meta.env.VITE_LLM_BASE_URL || 'http://*************:8080';
export const GIT_CONNECTION_URL =
  import.meta.env.VITE_GIT_CONNECTION_URL || 'http://*************:8081/container/start';

// LLM model configurations
const testCaseGenerationModel = import.meta.env.VITE_TEST_CASE_GENERATION_MODEL || 'qwen';
const testScriptGenerationModel = import.meta.env.VITE_TEST_SCRIPT_GENERATION_MODEL || 'qwencoder';

// API endpoints
export const LLM_TEST_CASE_GENERATION_URL = `${LLM_BASE_URL}/api/stream/testcase/${testCaseGenerationModel}`;
export const LLM_TEST_SCRIPT_GENERATION_URL = `${LLM_BASE_URL}/api/stream/codegeneration/${testScriptGenerationModel}`;
