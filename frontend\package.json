{"name": "react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@monaco-editor/react": "^4.7.0", "@tanstack/react-table": "^8.21.2", "@textea/json-viewer": "^4.0.1", "@types/axios": "^0.14.4", "@types/lodash": "^4.17.16", "@types/react-router-dom": "^5.3.3", "antd": "^5.24.4", "axios": "^1.8.4", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "prism-react-renderer": "^2.4.1", "puppeteer": "^24.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-redux": "^9.2.0", "react-router-dom": "^7.5.2", "vite": "^6.3.4"}, "devDependencies": {"@babel/helpers": "^7.26.10", "@babel/runtime": "^7.26.10", "@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.3.4"}}