package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.Project;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectRepository extends BaseRepository<Project, Long> {
    List<Project> findAllByOrderByUpdatedAtDesc();
    List<Project> findByCreatedBy(String createdBy);
    boolean existsByIdAndCreatedBy(Long id, String createdBy);

    @Query("""
            SELECT DISTINCT p FROM Project p
            WHERE p.createdBy = :username
            OR p.id IN (
                SELECT ea.entityId FROM EntityAccess ea
                WHERE ea.entityType.name = 'PROJECT'
                AND ea.sharedWithUser.id = :userId
            )
            ORDER BY p.updatedAt DESC
            """)
    List<Project> findBySharedWithUserOrderByUpdatedAtDesc(
            @Param("username") String username,
            @Param("userId") Long userId);
}