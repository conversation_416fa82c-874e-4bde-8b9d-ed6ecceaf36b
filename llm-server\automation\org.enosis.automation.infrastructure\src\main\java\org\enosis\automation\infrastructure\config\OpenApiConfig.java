package org.enosis.automation.infrastructure.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.context.annotation.Configuration;

@OpenAPIDefinition(
    info = @Info(
        title = "DeepSeek API",
        version = "1.0",
        description = "API for managing Artificial Intelligence"
    )
)
@Configuration
public class OpenApiConfig {
}