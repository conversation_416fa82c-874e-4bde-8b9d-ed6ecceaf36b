import { Project } from './project.types';
import { TreeNode } from './tree.types';

/**
 * Request DTO for Google Sheets import
 */
export interface GoogleSheetImportRequest {
  sheetUrl: string;
  submoduleColumn?: string;
  submoduleStartRow?: number;
  projectName?: string;
}

/**
 * Response DTO for Google Sheets import
 */
export interface GoogleSheetImportResponse {
  project: Project;
  projectTree: TreeNode;
}
