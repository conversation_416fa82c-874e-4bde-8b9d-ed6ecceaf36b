package com.enosisbd.app.security;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.core.user.OAuth2User;

import java.util.Collection;
import java.util.Map;

public class CustomOAuth2User implements OAuth2User {

  private final OAuth2User oauth2User;
  private final Map<String, Object> attributes;

  public CustomOAuth2User(OAuth2User oauth2User, Map<String, Object> attributes) {
    this.oauth2User = oauth2User;
    this.attributes = attributes;
  }

  @Override
  public Map<String, Object> getAttributes() {
    return attributes;
  }

  @Override
  public Collection<? extends GrantedAuthority> getAuthorities() {
    return oauth2User.getAuthorities();
  }

  @Override
  public String getName() {
    return oauth2User.getName();
  }

  public String getAccessToken() {
    return (String) attributes.get("access_token");
  }
}