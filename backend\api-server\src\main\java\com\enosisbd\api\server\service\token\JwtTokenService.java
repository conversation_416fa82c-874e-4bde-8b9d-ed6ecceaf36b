package com.enosisbd.api.server.service.token;

import com.nimbusds.jose.*;

import java.util.List;

public interface JwtTokenService {
    String generateAccessToken(String subject, List<String> roles, String authProvider) throws JOSEException;
    String generateRefreshToken(String subject, List<String> roles, String authProvider) throws JOSEException;
    boolean validateRefreshToken(String token);
    String getAuthProvider(String token);
    String extractUsername(String token);
}
