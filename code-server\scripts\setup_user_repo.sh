#!/bin/bash

# Script arguments
PROJECT_ID=$1
REPONAME=$2
GIT_REPO_URL=$3
GIT_USER_EMAIL=$4
ACCESS_TOKEN=$5
PROVIDER=${6:-github}  # Default to github if not provided

# Base directory for the user's projects
BASE_DIR="/home/<USER>/projects/$PROJECT_ID"
REPO_DIR="$BASE_DIR/$REPONAME"

# Debug information
echo "Setting up repository with the following parameters:"
echo "Project ID: $PROJECT_ID"
echo "Repository Name: $REPONAME"
echo "Git Repository URL: $GIT_REPO_URL"
echo "Git User Email: $GIT_USER_EMAIL"
echo "Provider: $PROVIDER"
echo "Base Directory: $BASE_DIR"
echo "Repository Directory: $REPO_DIR"

# Check if projects directory exists and has correct permissions
if [ ! -d "/home/<USER>/projects" ]; then
  echo "Creating projects directory"
  mkdir -p /home/<USER>/projects
fi

# Ensure projects directory has correct permissions
echo "Setting permissions for projects directory"
chmod -R 777 /home/<USER>/projects

# Create user folder if it doesn't exist
echo "Creating project directory: $BASE_DIR"
mkdir -p "$BASE_DIR" || { echo "Failed to create directory: $BASE_DIR"; exit 1; }
chmod 777 "$BASE_DIR" || { echo "Failed to set permissions for: $BASE_DIR"; exit 1; }
chown -R coder:coder "$BASE_DIR" || { echo "Failed to set ownership for: $BASE_DIR"; exit 1; }

# Clone the repository (with authentication for private repos)
if [ ! -d "$REPO_DIR/.git" ]; then
  # Modify the Git URL to include the token for authentication
  if [ "$PROVIDER" = "github" ]; then
    # GitHub uses token in URL
    GIT_AUTH_URL="https://$ACCESS_TOKEN@${GIT_REPO_URL#https://}"
  elif [ "$PROVIDER" = "bitbucket" ]; then
    # Bitbucket uses x-token-auth as username
    # First, clean the URL by removing any existing credentials
    CLEAN_URL=$(echo "$GIT_REPO_URL" | sed -E 's/https:\/\/[^@]+@/https:\/\//')
    GIT_AUTH_URL="https://x-token-auth:$ACCESS_TOKEN@${CLEAN_URL#https://}"
  else
    # Default fallback
    GIT_AUTH_URL="https://$ACCESS_TOKEN@${GIT_REPO_URL#https://}"
  fi

  echo "Cloning repository from $GIT_REPO_URL using $PROVIDER authentication"
  echo "Using auth URL format: ${GIT_AUTH_URL//$ACCESS_TOKEN/****}"

  # Create the repository directory with proper permissions
  echo "Creating repository directory: $REPO_DIR"
  mkdir -p "$REPO_DIR" || { echo "Error: Failed to create repository directory"; exit 1; }
  chmod 777 "$REPO_DIR" || { echo "Error: Failed to set permissions for repository directory"; exit 1; }

  # Clone with error handling
  echo "Cloning repository..."
  if ! git clone "$GIT_AUTH_URL" "$REPO_DIR"; then
    echo "Error: Failed to clone repository"
    # Try with different URL format for Bitbucket
    if [ "$PROVIDER" = "bitbucket" ]; then
      echo "Trying alternative Bitbucket URL format..."
      CLEAN_URL=$(echo "$GIT_REPO_URL" | sed -E 's/https:\/\/[^@]+@/https:\/\//')
      ALT_GIT_AUTH_URL="https://$ACCESS_TOKEN@${CLEAN_URL#https://}"
      echo "Using alternative auth URL format: ${ALT_GIT_AUTH_URL//$ACCESS_TOKEN/****}"
      if ! git clone "$ALT_GIT_AUTH_URL" "$REPO_DIR"; then
        echo "Error: Failed to clone repository with alternative URL format"
        exit 1
      fi
    else
      exit 1
    fi
  fi
fi

# Check if repository directory exists
if [ ! -d "$REPO_DIR" ]; then
  echo "Error: Repository directory does not exist after clone"
  exit 1
fi

# Navigate to the repository directory
echo "Changing to repository directory: $REPO_DIR"
cd "$REPO_DIR" || {
  echo "Error: Failed to change to repository directory"
  exit 1
}

# Configure Git user name and email (per repo)
echo "Configuring Git user name and email"
git config user.name "$GIT_USER_EMAIL"
git config user.email "$GIT_USER_EMAIL"

# Set permissions
echo "Setting final permissions"
chmod -R 777 "$BASE_DIR"
chown -R coder:coder "$BASE_DIR" || echo "Warning: Failed to set ownership, but continuing..."

echo "✅ Repo setup done for $PROJECT_ID → $REPONAME"