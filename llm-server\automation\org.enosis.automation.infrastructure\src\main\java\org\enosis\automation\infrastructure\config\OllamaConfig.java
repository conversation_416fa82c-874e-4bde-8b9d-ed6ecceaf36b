package org.enosis.automation.infrastructure.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.Scope;

@Configuration
public class OllamaConfig {

    @Value("${spring.ai.ollama.base-url}")
    private String ollamaServerBaseUrl;
    @Value("${configuration.temp}")
    private Double temp;
    @Value("${configuration.topP}")
    private Double topP;
    @Value("${configuration.numKeep}")
    private Integer numKeep;
    @Value("${configuration.seed}")
    private Integer seed;
    @Value("${configuration.numPredict}")
    private Integer numPredict;

    @Bean
    @Qualifier("deepSeekChatClient")
    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    public ChatClient deepSeekChatClient() {
        return ChatClient.create(
                OllamaChatModel.builder()
                        .defaultOptions(
                                OllamaOptions.builder()
                                        .model("deepseek-r1:14b")
                                        .temperature(temp)
                                        .topP(topP)
                                        .numKeep(numKeep)
                                        .seed(seed)
                                        .numPredict(numPredict)
                                        .build()
                        )
                        .ollamaApi(new OllamaApi(ollamaServerBaseUrl))
                        .build()
        );
    }

    @Bean
    @Qualifier("qwenChatClient")
    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    public ChatClient qwenChatClient() {
        return ChatClient.create(
                OllamaChatModel.builder()
                        .defaultOptions(
                                OllamaOptions.builder()
                                        .model("qwen2.5:7b")
                                        .temperature(temp)
                                        .topP(topP)
                                        .numKeep(numKeep)
                                        .seed(seed)
                                        .numPredict(numPredict)
                                        .build()
                        )
                        .ollamaApi(new OllamaApi(ollamaServerBaseUrl))
                        .build()
        );
    }

    @Bean
    @Qualifier("starCoderChatClient")
    public ChatClient starCoderChatClient() {
        return ChatClient.create(
                OllamaChatModel.builder()
                        .defaultOptions(
                                OllamaOptions.builder()
                                        .model("starcoder2:15b")
                                        .temperature(temp)
                                        .topP(topP)
                                        .numKeep(numKeep)
                                        .seed(seed)
                                        .numPredict(numPredict)
                                        .build()
                        )
                        .ollamaApi(new OllamaApi(ollamaServerBaseUrl))
                        .build()
        );
    }

    @Bean
    @Qualifier("qwenCoderChatClient")
    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    public ChatClient qwenCoderChatClient() {
        return ChatClient.create(
                OllamaChatModel.builder()
                        .defaultOptions(
                                OllamaOptions.builder()
                                        .model("qwen2.5-coder:7b")
                                        .temperature(temp)
                                        .topP(topP)
                                        .numKeep(numKeep)
                                        .seed(seed)
                                        .numPredict(numPredict)
                                        .build()
                        )
                        .ollamaApi(new OllamaApi(ollamaServerBaseUrl))
                        .build()
        );
    }

    @Bean
    @Qualifier("codeLlamaChatClient")
    public ChatClient codeLlamaChatClient() {
        return ChatClient.create(
                OllamaChatModel.builder()
                        .defaultOptions(
                                OllamaOptions.builder()
                                        .model("codellama:13b")
                                        .temperature(temp)
                                        .topP(topP)
                                        .numKeep(numKeep)
                                        .seed(seed)
                                        .numPredict(numPredict)
                                        .build()
                        )
                        .ollamaApi(new OllamaApi(ollamaServerBaseUrl))
                        .build()
        );
    }
}
