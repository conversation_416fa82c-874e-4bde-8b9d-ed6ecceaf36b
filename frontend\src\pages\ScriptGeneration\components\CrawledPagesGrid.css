.crawled-pages-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  background-color: var(--color-white);
  box-shadow: var(--box-shadow-default);
  padding-bottom: var(--spacing-lg);
}

.content-area {
  flex-grow: 1;
  overflow-y: auto;
  padding: 0 var(--spacing-lg);
}

.form-container {
  max-width: 100%;
}

.table-container {
  overflow-x: auto;
  margin-top: var(--spacing-md);
  border-radius: var(--border-radius-lg);
}

.button-row {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

.disabled-row {
  opacity: 0.7;
  pointer-events: none;
  background-color: var(--disabled-bg);
}

.show-auth-button {
  margin: 0 auto;
}

.text-right {
  text-align: right;
}

.top-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg) var(--spacing-lg) 0;
}

.top-actions .btn-sm {
  padding: var(--spacing-xs) var(--spacing-md) !important;
  font-size: var(--font-size-sm) !important;
  height: 30px;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.top-actions .btn-primary {
  background-color: var(--primary-color);
  border: none;
  color: var(--color-white);
}

.top-actions .btn-black {
  background-color: var(--primary-black);
  border: none;
  color: var(--color-white);
}

.top-actions .btn-primary:hover {
  background-color: var(--primary-hover);
}

.top-actions .btn-black:hover {
  background-color: var(--color-black);
}

/* Icon styles */
.add-icon {
  font-size: var(--icon-size-sm);
}

/* Enhanced input styles */
.crawled-page-input {
  width: 100%;
  padding: 6px var(--spacing-md);
  border: 1px solid var(--input-border-color);
  border-radius: var(--border-radius-md);
  transition: var(--transition-default);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  background-color: var(--color-white);
  height: 32px;
  box-sizing: border-box;
}

.crawled-page-input:disabled {
  background-color: var(--disabled-bg);
  color: var(--disabled-color);
  cursor: not-allowed;
}

/* Checkbox styling */
.skip-hidden-checkbox {
  display: flex;
  justify-content: center;
}

/* Action buttons */
.page-action-button {
  margin: 0 var(--spacing-xs);
}

.ant-table-wrapper .ant-table-tbody > tr > td {
  align-content: center;
}
