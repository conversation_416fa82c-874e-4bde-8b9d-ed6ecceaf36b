import { ConfigProvider } from 'antd';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import './App.css';
import { AuthProvider } from './components/ContextApi/AuthContext';
import { Layout } from './components/layout/Layout';
import AppRoutes from './routes/AppRoutes';

const App: React.FC = () => (
  <ConfigProvider
    theme={{
      token: {
        colorPrimary: '#ea2a35',
        fontFamily: 'Poppins',
        borderRadius: 4,
      },
    }}
  >
    <BrowserRouter>
      <AuthProvider>
        <Layout>
          <AppRoutes />
        </Layout>
      </AuthProvider>
    </BrowserRouter>
  </ConfigProvider>
);

export default App;
