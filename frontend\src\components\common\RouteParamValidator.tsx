import { Spin } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ProjectService } from '../../services/project.service';
import { Project } from '../../types/project.types';

interface ValidatedResources {
  project?: Project;
}

interface RouteParamValidatorProps {
  requiredParams: string[];
  component: React.ComponentType<ValidatedResources>;
}

const validateParams = async (
  params: Record<string, string>,
): Promise<{
  valid: boolean;
  resources: ValidatedResources;
  message?: string;
  status?: number;
}> => {
  const resources: ValidatedResources = {};

  try {
    for (const [key, value] of Object.entries(params)) {
      switch (key) {
        case 'projectId': {
          resources.project = await ProjectService.getProjectById(Number(value));
          break;
        }
      }
    }
    return { valid: true, resources };
  } catch (error: any) {
    return {
      valid: false,
      resources: {},
      message: error.message || 'Resource not found',
      status: error.status || 404,
    };
  }
};

const RouteParamValidator: React.FC<RouteParamValidatorProps> = ({
  requiredParams,
  component: ComponentToRender,
  ...rest
}) => {
  const params = useParams<Record<string, string>>();
  const navigate = useNavigate();
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [validatedResources, setValidatedResources] = useState<ValidatedResources>({});
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [errorStatus, setErrorStatus] = useState<number>(500);

  useEffect(() => {
    const checkParams = async () => {
      setIsValid(null);
      const missingParams = requiredParams.filter((p) => !params[p]);

      if (missingParams.length > 0) {
        setErrorMessage(`Missing required URL parameters: ${missingParams.join(', ')}`);
        setErrorStatus(400);
        setIsValid(false);
        return;
      }

      const paramsToValidate = requiredParams.reduce((acc, key) => {
        if (params[key]) {
          acc[key] = params[key] as string;
        }
        return acc;
      }, {} as Record<string, string>);

      const validationResult = await validateParams(paramsToValidate);

      if (!validationResult.valid) {
        setErrorMessage(validationResult.message || 'Invalid URL parameters.');
        setErrorStatus(validationResult.status || 400);
        setIsValid(false);
      } else {
        setValidatedResources(validationResult.resources);
        setIsValid(true);
      }
    };

    checkParams();
  }, [params, requiredParams]);

  useEffect(() => {
    if (isValid === false) {
      if (errorStatus === 404) {
        navigate('/404', { replace: true });
      } else if (errorStatus === 403) {
        navigate('/403', { replace: true });
      } else if (errorStatus === 500) {
        navigate('/500', { replace: true });
      } else {
        navigate('/error', {
          replace: true,
          state: { message: errorMessage, status: errorStatus },
        });
      }
    }
  }, [isValid, navigate, errorMessage, errorStatus]);

  if (isValid === null) {
    return (
      <div className="loading">
        <Spin size="large" />
      </div>
    );
  }

  if (isValid === true) {
    return <ComponentToRender {...validatedResources} {...rest} />;
  }

  return null;
};

export default RouteParamValidator;
