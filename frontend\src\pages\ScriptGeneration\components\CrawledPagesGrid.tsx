import { DeleteOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { Button, Checkbox, Col, Form, Modal, Popconfirm, Row, Space, Table, Tooltip } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { AuthConfig } from '../../../types/web-crawler.types';
import crawledPageService from '../../../services/crawledpage.service';
import { CrawlerService } from '../../../services/crawler.service';
import { AuthType } from '../../../types/automate-process.types';
import { CrawlOption, CrawledPage } from '../../../types/crawled-page.types';
import { AuthForm } from './AuthForm';
import './CrawledPagesGrid.css';
import { hasAnyEmptyField } from '../../../utils/common.utils';

interface CrawledPagesGridProps {
  testSuiteId?: number;
  projectId?: number;
  crawledPages: CrawledPage[];
  isLoading: boolean;
  fetchCrawledPages: () => Promise<void>;
  onClose: () => void;
  selectedRowKeys: React.Key[];
  setSelectedRowKeys: (keys: React.Key[]) => void;
}

const CrawledPagesGrid: React.FC<CrawledPagesGridProps> = ({
  projectId,
  crawledPages: initialCrawledPages,
  isLoading,
  fetchCrawledPages,
  onClose,
  selectedRowKeys,
  setSelectedRowKeys,
}) => {
  const [localCrawledPages, setLocalCrawledPages] = useState<CrawledPage[]>([]);
  const [loadingRows, setLoadingRows] = useState<Record<string | number, boolean>>({});
  const [form] = Form.useForm();
  const [showAuth, setShowAuth] = useState(false);
  const [authType, setAuthType] = useState<AuthType>('none');
  const [authConfig, setAuthConfig] = useState<AuthConfig>({
    credentials: undefined,
    bearerToken: undefined,
    localStorage: undefined,
    cookies: undefined,
  });

  useEffect(() => {
    setLocalCrawledPages([...initialCrawledPages]);
  }, [initialCrawledPages]);

  const newRowCounter = useRef(0);

  useEffect(() => {
    if (authType === 'none') {
      setAuthConfig({
        credentials: undefined,
        bearerToken: undefined,
        localStorage: undefined,
        cookies: undefined,
      });
    }
  }, [authType]);

  const handleAddPage = () => {
    const newPage: CrawledPage = {
      id: `temp-${newRowCounter.current++}`, // Add temporary ID for new rows
      pageName: '',
      pageUrl: '',
      projectId: projectId,
      crawlOption: {
        skipHidden: false,
        needsLogin: true,
        skipTags: [],
        skipCssSelectors: [],
        onlyTags: [],
        onlyCssSelectors: [],
      },
      domJson: { url: '', el: [] },
      isCrawled: false,
    };
    setLocalCrawledPages([...localCrawledPages, newPage]);
  };

  const handleDeletePage = async (recordId: string | number, index: number) => {
    if (typeof recordId === 'string' && recordId.startsWith('temp-')) {
      const newPages = [...localCrawledPages];
      newPages.splice(index, 1);
      setLocalCrawledPages(newPages);
    } else if (typeof recordId === 'number') {
      setLoadingRows((prev) => ({ ...prev, [recordId]: true }));
      try {
        await crawledPageService.deleteCrawledPage(recordId);
        await fetchCrawledPages();
      } catch (error) {
        console.error('Failed to delete crawled page:', error);
        Modal.error({
          title: 'Delete Failed',
          content: 'Could not delete the page. Please try again.',
        });
      } finally {
        setLoadingRows((prev) => {
          const newState = { ...prev };
          delete newState[recordId];
          return newState;
        });
      }
    }
  };

  const handleInputChange = (index: number, field: keyof CrawledPage, value: unknown) => {
    const newPages = [...localCrawledPages];
    if (field === 'crawlOption' && typeof value === 'object' && value !== null) {
      newPages[index] = {
        ...newPages[index],
        crawlOption: { ...(newPages[index].crawlOption || {}), ...value } as CrawlOption,
      };
    } else {
      newPages[index] = { ...newPages[index], [field]: value };
    }
    setLocalCrawledPages(newPages);
  };

  const handleCrawlOptionChange = (index: number, field: keyof CrawlOption, value: unknown) => {
    const newPages = [...localCrawledPages];
    newPages[index] = {
      ...newPages[index],
      crawlOption: {
        ...(newPages[index].crawlOption || { skipHidden: false, needsLogin: true }),
        [field]: value,
      },
    };
    setLocalCrawledPages(newPages);
  };

  const needsCrawl = (page: CrawledPage): boolean => {
    return isNewPage(page);
  };

  const isNewPage = (page: CrawledPage): boolean => {
    return typeof page.id === 'string' && page.id.startsWith('temp-');
  };

  const handleRecrawlPage = async (index: number) => {
    const page = localCrawledPages[index];
    const pageId = page.id;
    setLoadingRows((prev) => ({ ...prev, [pageId]: true }));

    try {
      const updatedPageData = await crawlPage(page);

      if (typeof page.id === 'string' && page.id.startsWith('temp-')) {
        await crawledPageService.createCrawledPage(updatedPageData);
      } else if (typeof page.id === 'number') {
        await crawledPageService.updateCrawledPage(page.id, updatedPageData);
      } else {
        console.error('Cannot save page with invalid ID:', page.id);
        throw new Error(`Invalid page ID: ${page.id}`);
      }
      await fetchCrawledPages();
    } catch (error) {
      console.error(`Failed to recrawl or save URL: ${page.pageUrl}`, error);
      Modal.error({
        title: 'Recrawl Failed',
        content: `Could not recrawl ${page.pageUrl}. Please check the URL and authentication, then try again.`,
      });
    } finally {
      setLoadingRows((prev) => {
        const newState = { ...prev };
        delete newState[pageId];
        return newState;
      });
    }
  };

  const handleCrawl = async () => {
    const pagesToProcess = [...localCrawledPages];
    let changesMade = false;

    const initialLoadingRows: Record<string | number, boolean> = {};
    pagesToProcess.forEach((p) => {
      if (needsCrawl(p)) initialLoadingRows[p.id] = true;
    });

    setLoadingRows(initialLoadingRows);

    const processedPages = await Promise.all(
      pagesToProcess.map(async (page) => {
        if (!needsCrawl(page)) {
          return page;
        }

        try {
          const updatedPage = await crawlPage(page);

          changesMade = true;
          return updatedPage;
        } catch (error) {
          console.error(`Failed to crawl URL: ${page.pageUrl}`, error);
          setLoadingRows((prev) => {
            const newState = { ...prev };
            delete newState[page.id];
            return newState;
          });
          return page;
        }
      }),
    );

    if (changesMade) {
      const pagesToSave = processedPages
        .map((page) => {
          if (typeof page.id === 'string' && page.id.startsWith('temp-')) {
            return { ...page, id: undefined };
          }
          return page;
        })
        .filter((p) => p !== null);

      try {
        await crawledPageService.saveOrUpdateBatch(pagesToSave as CrawledPage[]);
        await fetchCrawledPages();
        setLoadingRows({});
        onClose();
      } catch (error) {
        console.error('Failed to save batch:', error);
        Modal.error({
          title: 'Save Failed',
          content: 'Could not save all pages. Please review the pages and try again.',
        });
        setLoadingRows({});
      }
    } else {
      setLoadingRows({});
      onClose();
    }
  };

  const crawlPage = async (page: CrawledPage) => {
    const crawlResult = await CrawlerService.getCrawledDOMJson(
      page.pageUrl,
      page.pageName,
      authConfig,
      page.crawlOption,
    );
    const safeCrawlResult = crawlResult || { url: page.pageUrl, el: [] };
    if (!safeCrawlResult.url) safeCrawlResult.url = page.pageUrl;
    if (!Array.isArray(safeCrawlResult.el)) safeCrawlResult.el = [];

    const updatedPageData = {
      ...page,
      domJson: safeCrawlResult,
      isCrawled: safeCrawlResult.el.length > 0,
    };
    return updatedPageData;
  };

  const isPageComplete = (page: CrawledPage): boolean => {
    return !!(page.pageName?.trim() && page.pageUrl?.trim());
  };

  const canAddNewPage = (): boolean => {
    return localCrawledPages.length === 0 || localCrawledPages.every(isPageComplete);
  };

  const disabledSaveButton = (): boolean => {
    return (
      isLoading ||
      !!Object.keys(loadingRows).length ||
      !localCrawledPages.every(isPageComplete) ||
      (localCrawledPages
        .filter((p) => p.id.toString().includes('temp'))
        .some((p) => p.crawlOption.needsLogin) &&
        isInvalidAuth(authConfig))
    );
  };

  const disabledCrawlButton = (index: number): boolean => {
    return (
      isLoading ||
      ((localCrawledPages[index].crawlOption?.needsLogin ?? false) && isInvalidAuth(authConfig))
    );
  };

  const isInvalidAuth = (auth: AuthConfig): boolean => {
    if (auth == null || authType == 'none') return true;
    else if (authType == 'credentials') return hasAnyEmptyField(auth.credentials);
    else if (authType == 'bearer')
      return auth.bearerToken == null || auth.bearerToken.trim() === '';
    else if (authType == 'cookies')
      return (
        (Array.isArray(auth.cookies) && auth.cookies.length === 0) || hasAnyEmptyField(auth.cookies)
      );
    else if (authType == 'localStorage')
      return (
        (Array.isArray(auth.localStorage) && auth.localStorage.length === 0) ||
        hasAnyEmptyField(auth.localStorage)
      );
    else return false;
  };

  const columns = [
    {
      title: 'Page Name',
      dataIndex: 'pageName',
      key: 'pageName',
      width: '25%',
      render: (_: unknown, record: CrawledPage, index: number) => (
        <input
          className="crawled-page-input"
          value={record.pageName}
          onChange={(e) => handleInputChange(index, 'pageName', e.target.value)}
          placeholder="Page Name"
          required
          disabled={loadingRows[record.id]}
        />
      ),
    },
    {
      title: 'URL',
      dataIndex: 'pageUrl',
      key: 'pageUrl',
      width: '50%',
      render: (_: unknown, record: CrawledPage, index: number) => (
        <input
          className="crawled-page-input"
          value={record.pageUrl}
          onChange={(e) => handleInputChange(index, 'pageUrl', e.target.value)}
          placeholder="URL"
          required
          disabled={loadingRows[record.id]}
        />
      ),
    },
    {
      title: 'Needs Login',
      dataIndex: ['crawlOption', 'needsLogin'],
      key: 'needsLogin',
      width: '10%',
      render: (_: unknown, record: CrawledPage, index: number) => (
        <div className="skip-hidden-checkbox">
          <Checkbox
            checked={!!record.crawlOption?.needsLogin}
            onChange={(e) => handleCrawlOptionChange(index, 'needsLogin', e.target.checked)}
            disabled={loadingRows[record.id]}
          />
        </div>
      ),
    },
    {
      title: 'Skip Hidden',
      dataIndex: ['crawlOption', 'skipHidden'],
      key: 'skipHidden',
      width: '10%',
      render: (_: unknown, record: CrawledPage, index: number) => (
        <div className="skip-hidden-checkbox">
          <Checkbox
            checked={!!record.crawlOption?.skipHidden}
            onChange={(e) => handleCrawlOptionChange(index, 'skipHidden', e.target.checked)}
            disabled={loadingRows[record.id]}
          />
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'action',
      width: '15%',
      render: (_: unknown, record: CrawledPage, index: number) => (
        <Space>
          {record.isCrawled && (
            <Tooltip title="Recrawl">
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={() => handleRecrawlPage(index)}
                size="small"
                loading={loadingRows[record.id]}
                disabled={disabledCrawlButton(index)}
                className="page-action-button"
              />
            </Tooltip>
          )}
          <Tooltip title="Delete">
            <Popconfirm
              title="Sure to delete?"
              onConfirm={() => handleDeletePage(record.id, index)}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
                disabled={loadingRows[record.id] || isLoading}
                className="page-action-button"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
    type: 'checkbox' as const, // Enables multiple selection
  };

  return (
    <div className="crawled-pages-container">
      <div className="top-actions">
        <Button
          type="primary"
          icon={<PlusOutlined className="add-icon" />}
          onClick={handleAddPage}
          size="small"
          className="btn-primary btn-sm"
          disabled={isLoading || !canAddNewPage()}
        >
          Add URL
        </Button>
        <button
          type="button"
          onClick={() => {
            setShowAuth(!showAuth);
            if (!showAuth) setAuthType('none');
          }}
          className="btn-black btn-sm"
          disabled={isLoading}
        >
          {showAuth ? 'Hide Authentication' : 'Show Authentication'}
        </button>
      </div>

      <div className="content-area">
        <Form form={form} layout="vertical" className="form-container">
          {showAuth && (
            <AuthForm
              authType={authType}
              setAuthType={setAuthType}
              onAuthChange={(newAuthConfig) => setAuthConfig(newAuthConfig)}
            />
          )}

          <Table
            dataSource={localCrawledPages}
            rowSelection={rowSelection}
            columns={columns}
            rowKey={(record) => record.id?.toString() || record.id}
            pagination={false}
            loading={isLoading}
            size="small"
            scroll={{ x: 650 }}
            className="table-container crawled-pages-table"
            bordered
            rowClassName={(record) => {
              const rowId = typeof record.id === 'number' ? record.id : record.id?.toString();
              return loadingRows[rowId] ? 'disabled-row' : '';
            }}
          />

          <Row className="button-row">
            <Col span={12}></Col>
            <Col span={12} className="text-right">
              <Space>
                <button
                  type="button"
                  className="btn-secondary btn-sm"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn-primary btn-sm"
                  onClick={handleCrawl}
                  disabled={disabledSaveButton()}
                >
                  Save
                </button>
              </Space>
            </Col>
          </Row>
        </Form>
      </div>
    </div>
  );
};

export default CrawledPagesGrid;
