@enosisServer = http://*************:8080
@localServer = http://localhost:9090
@baseUrl = {{localServer}}

### Get all projects
GET {{baseUrl}}/api/projects


### Create a project
POST {{baseUrl}}/api/projects
Content-Type: application/json

{
    "name": "Test Project",
    "description": "This is a test project"
}


@projectId = 8


### Get a project
GET {{baseUrl}}/api/projects/{{projectId}}


### Update a project
PUT {{baseUrl}}/api/projects/{{projectId}}
Content-Type: application/json

{
    "name": "Test Project - updated",
    "description": "This is a test project - updated"
}


### Delete a project
DELETE {{baseUrl}}/api/projects/{{projectId}}

### Get all versions
GET {{baseUrl}}/api/versions

### Create a version
POST {{baseUrl}}/api/versions
Content-Type: application/json

{
    "name": "First Version",
    "versionNumber": "1.0.0",
    "projectId": {{projectId}},
    "testCaseGenerationMethod": "ai",
    "generateAutomationScript": true,
    "platform": "Selenium",
    "language": "Java"
}


@versionId = 1


### Get a version seperately
GET {{baseUrl}}/api/versions/{{versionId}}


### Update version
PUT {{baseUrl}}/api/versions/{{versionId}}
Content-Type: application/json

{
    "name": "Fifth Test Version",
    "versionNumber": "5.0.0",
    "projectId": "{{projectId}}",
    "testCaseGenerationMethod": "manual",
    "generateAutomationScript": true,
    "platform": "Playwright",
    "language": "TypeScript"
}

### Delete a version
DELETE {{baseUrl}}/api/versions/{{versionId}}


### Get all versions of project
GET {{baseUrl}}/api/projects/{{projectId}}/versions


### get all test suits
GET {{baseUrl}}/api/test-suites


### create a test suit
POST {{baseUrl}}/api/test-suites
Content-Type: application/json

{
    "requirement": "this is dummy requirement",
    "versionId": {{versionId}}
}


@testSuiteId = 2


### Get a test suit
GET {{baseUrl}}/api/test-suites/{{testSuiteId}}

### Update a test suit
PUT {{baseUrl}}/api/test-suites/{{testSuiteId}}
Content-Type: application/json

{
    "requirement": "this is dummy requirement - updated"
}


### Delete a test suit
DELETE {{baseUrl}}/api/test-suites/{{testSuiteId}}


### Get all test suits of a version
GET {{baseUrl}}/api/versions/{{versionId}}/test-suites


### Get all test cases
GET {{baseUrl}}/api/test-cases


### Create a test case
POST {{baseUrl}}/api/test-cases
Content-Type: application/json

{
    "description": "login with invalid case",
    "steps": [
        "enter valid username",
        "enter invalid password",
        "click login button"
    ],
    "expectedResult": "check if the page is /login and an error message (Invalid username or password) is displayed",
    "testSuiteId": {{testSuiteId}}
}

@testCaseId = 3


### Get a test case
GET {{baseUrl}}/api/test-cases/{{testCaseId}}


### Update a test case
PUT {{baseUrl}}/api/test-cases/{{testCaseId}}
Content-Type: application/json

{
    "description": "login with invalid case updated",
    "steps": [
        "enter valid username",
        "enter invalid password",
        "click login button"
    ],
    "expectedResult": "check if the page is /login and an error message (Invalid username or password) is displayed",
    "testSuiteId": {{testSuiteId}}
}


### Delete a test case
DELETE {{baseUrl}}/api/test-cases/{{testCaseId}}


### Get all test cases for a test suit
GET {{baseUrl}}/api/test-suites/{{testSuiteId}}/test-cases


### Get all test scripts
GET {{baseUrl}}/api/test-scripts

### Create a test script
POST {{baseUrl}}/api/test-scripts
Content-Type: application/json

{
    "code": "console.log('hello world');",
    "testSuiteId": {{testSuiteId}}
}

@testScriptId = 1

### Get a test script
GET {{baseUrl}}/api/test-scripts/{{testScriptId}}

### Update a test script
PUT {{baseUrl}}/api/test-scripts/{{testScriptId}}
Content-Type: application/json

{
    "code": "console.log('hello world - updated');",
    "testSuiteId": {{testSuiteId}}
}

### Delete a test script
DELETE {{baseUrl}}/api/test-scripts/{{testScriptId}}

### Get test script for a test suit
GET {{baseUrl}}/api/test-suites/{{testSuiteId}}/test-script

### Get all crawled pages
GET {{baseUrl}}/api/crawled-pages

### Create a crawled page
POST {{baseUrl}}/api/crawled-pages
Content-Type: application/json

{
    "pageName": "login page",
    "pageUrl": "http://localhost:3000/login",
    "crawlOption": {
        "skipHidden": false,
        "onlyTags": ["input", "button"]
    },
    "testSuiteId": {{testSuiteId}}
}


@crawledPageId = 5


### Get a crawled page
GET {{baseUrl}}/api/crawled-pages/{{crawledPageId}}

### Update a crawled page
PUT {{baseUrl}}/api/crawled-pages/{{crawledPageId}}
Content-Type: application/json

{
    "pageName": "login page updated",
    "pageUrl": "http://localhost:3000/login",
    "crawlOption": {
        "skipHidden": false,
        "onlyTags": ["input", "button"]
    },
    "domJson": {
        "one": 1,
        "two": [1,2],
        "three": {
            "four": 4,
            "five": 5
        }
    },
    "testSuiteId": {{testSuiteId}}
}

### Delete a crawled page
DELETE {{baseUrl}}/api/crawled-pages/{{crawledPageId}}

### Get all crawled pages for a test suit
GET {{baseUrl}}/api/test-suites/{{testSuiteId}}/crawled-pages

