package org.enosis.automation.infrastructure.config;

import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.ollama.OllamaChatModel;
import dev.langchain4j.model.ollama.OllamaStreamingChatModel;
import dev.langchain4j.rag.query.transformer.CompressingQueryTransformer;
import dev.langchain4j.rag.query.transformer.QueryTransformer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
public class LangchainConfig {

    @Value("${spring.ai.ollama.base-url}")
    private String modelBaseEndpoint;
    @Value("${configuration.temp}")
    private Double temp;
    @Value("${configuration.topP}")
    private Double topP;
    @Value("${configuration.numKeep}")
    private Integer numKeep;
    @Value("${configuration.seed}")
    private Integer seed;
    @Value("${configuration.numPredict}")
    private Integer numPredict;

    @Bean
    public StreamingChatLanguageModel qwenModel() {
        return OllamaStreamingChatModel.builder()
                .baseUrl(modelBaseEndpoint)
                .modelName("qwen2.5:7b")
                .temperature(temp)
                .topP(topP)
                .topK(numKeep)
                .seed(seed)
                .numPredict(numPredict)
                .timeout(Duration.ofSeconds(60))
                .logResponses(true)
                .logRequests(true)
                .build();
    }

    @Bean
    public QueryTransformer queryTransformer() {
        return new CompressingQueryTransformer(
                OllamaChatModel.builder()
                        .baseUrl(modelBaseEndpoint)
                        .modelName("qwen2.5:7b")
                        .temperature(temp)
                        .topP(topP)
                        .topK(numKeep)
                        .seed(seed)
                        .numPredict(numPredict)
                        .timeout(Duration.ofSeconds(60))
                        .build()
        );
    }

    @Bean
    public StreamingChatLanguageModel qwenCoderModel() {
        return OllamaStreamingChatModel.builder()
                .baseUrl(modelBaseEndpoint)
                .modelName("qwen2.5-coder:7b")
                .modelName("qwen2.5:7b")
                .temperature(temp)
                .topP(topP)
                .topK(numKeep)
                .seed(seed)
                .numPredict(numPredict)
                .timeout(Duration.ofSeconds(60))
                .build();
    }

    @Bean
    public StreamingChatLanguageModel deepSeekModel() {
        return OllamaStreamingChatModel.builder()
                .baseUrl(modelBaseEndpoint)
                .modelName("deepseek-r1:14b")
                .modelName("qwen2.5:7b")
                .temperature(temp)
                .topP(topP)
                .topK(numKeep)
                .seed(seed)
                .numPredict(numPredict)
                .timeout(Duration.ofSeconds(60))
                .build();
    }

}
