package com.enosisbd.app.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    private final CustomOAuth2UserService customOAuth2UserService;

    public SecurityConfig(CustomOAuth2UserService customOAuth2UserService) {
        this.customOAuth2UserService = customOAuth2UserService;
    }

  /*@Bean
  public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
    return http
        .authorizeExchange(authorizeExchangeSpec -> {
          authorizeExchangeSpec
              .pathMatchers("/**").permitAll()
              .anyExchange().authenticated();
        })
        .csrf(ServerHttpSecurity.CsrfSpec::disable)
        .cors(CorsSpec::disable)
        .oauth2ResourceServer(oAuth2 -> oAuth2
            .jwt(jwt -> jwt.jwtDecoder(reactiveJwtDecoder()))
        )
        .build();
  }

  @Bean
  public ReactiveJwtDecoder reactiveJwtDecoder() {
    String jwksUri = "http://localhost:8090/.well-known/jwks.json";
    return NimbusReactiveJwtDecoder.withJwkSetUri(jwksUri).build();
  }*/


    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(httpSecurityCsrfConfigurer ->
                        httpSecurityCsrfConfigurer.ignoringRequestMatchers("/ws/**", "/static/**", "/auth/**"))
                .headers(httpSecurityHeadersConfigurer ->
                        httpSecurityHeadersConfigurer.frameOptions(
                                frameOptionsConfig -> frameOptionsConfig.sameOrigin()
                        )
                )
                .formLogin(
                        AbstractHttpConfigurer::disable
                )
                .httpBasic(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers("/auth/*").permitAll()
                        .requestMatchers("/oauth2/authorization/**").permitAll()
                        .requestMatchers("/login/oauth2/code/**").permitAll()
                        .requestMatchers("/**").authenticated()
                )
                .oauth2Login(oauth2 -> oauth2
                        .userInfoEndpoint(userInfo -> userInfo
                                .userService(customOAuth2UserService)
                        )
                        .defaultSuccessUrl("/auth/callback", true)
                );
        return http.build();
    }
}
