package com.enosisbd.api.server.repository;

import com.enosisbd.api.server.entity.SubModule;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SubModuleRepository extends BaseRepository<SubModule, Long> {

    @Query("from SubModule sm left join fetch sm.module where sm.id = :id")
    Optional<SubModule> findByIdJoined(Long id);

    @Query("from SubModule sm left join fetch sm.module m where m.id = :moduleId")
    List<SubModule> findByModuleIdJoined(Long moduleId);

    @Query("from SubModule sm where sm.module.id = :moduleId")
    List<SubModule> findByModuleId(Long moduleId);

    /**
     * Find all submodules for a list of module IDs with joined module data
     * This optimizes database calls by fetching all submodules in a single query
     *
     * @param moduleIds List of module IDs
     * @return List of submodules with joined module data
     */
    @Query("from SubModule sm left join fetch sm.module m where m.id in :moduleIds")
    List<SubModule> findByModuleIdInJoined(@Param("moduleIds") List<Long> moduleIds);
}
