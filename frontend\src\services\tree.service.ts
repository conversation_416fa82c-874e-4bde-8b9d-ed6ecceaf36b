import { RestResponse } from '../types/common.types';
import { TreeNode } from '../types/tree.types';
import httpService from './http.service';

const API_ENDPOINTS = {
  PROJECT_TREE: (projectId: number) => `/api/projects/${projectId}/tree`,
};

/**
 * Service for tree structure operations
 */
const TreeService = {
  /**
   * Get the tree structure for a project
   * @param projectId The project ID
   * @returns The tree structure
   */
  getProjectTree: async (projectId: number): Promise<TreeNode> => {
    const response = await httpService.get<RestResponse<TreeNode>>(
      'common',
      API_ENDPOINTS.PROJECT_TREE(projectId),
    );
    return response.data;
  },

  /**
   * Transform the tree structure for Ant Design Tree component
   * @param treeNode The tree node from the API
   * @returns The transformed tree node for Ant Design Tree
   */
  transformTreeForAntDesign: (treeNode: TreeNode): TreeNode => {
    const transformNode = (node: TreeNode): TreeNode => {
      const transformedNode: TreeNode = {
        ...node,
        key: `${node.type}-${node.id}`,
        title: node.name,
        children: node.children.map(transformNode),
      };
      return transformedNode;
    };

    return transformNode(treeNode);
  },
};

export default TreeService;
