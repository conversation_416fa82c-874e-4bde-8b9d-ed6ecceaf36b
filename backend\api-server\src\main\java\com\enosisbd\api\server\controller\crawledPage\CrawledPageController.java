package com.enosisbd.api.server.controller.crawledPage;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.CrawledPageDto;
import com.enosisbd.api.server.exception.NotFoundRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import com.enosisbd.api.server.service.crawledPage.CrawledPageService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/crawled-pages")
@RequiredArgsConstructor
@Tag(name = "Crawled Page Controller", description = "APIs for crawled page management")
public class CrawledPageController implements CrawledPageApi {
    private final CrawledPageService service;

    @PostMapping
    @Operation(summary = "Create a new crawled page")
    @ApiResponse(responseCode = "201", description = "Crawled page created successfully")
    @Override
    public ResponseEntity<RestResponse<CrawledPageDto>> add(@Valid @RequestBody CrawledPageDto dto) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(service.add(dto)));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get crawled page by ID")
    @RequiresEntityAccess(entityType = EntityType.CRAWLED_PAGE)
    @Override
    public RestResponse<CrawledPageDto> getById(@PathVariable Long id) {
        return service.getById(id)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Crawled page not found with ID: " + id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update crawled page")
    @RequiresEntityAccess(entityType = EntityType.CRAWLED_PAGE)
    @Override
    public RestResponse<CrawledPageDto> update(@PathVariable Long id, @Valid @RequestBody CrawledPageDto dto) {
        dto.setId(id);
        return service.update(dto)
                .map(RestResponse::of)
                .orElseThrow(() -> NotFoundRestException.with("Crawled page not found with ID: " + id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete crawled page")
    @ApiResponse(responseCode = "204", description = "Crawled page deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.CRAWLED_PAGE)
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        boolean deleted = service.delete(id).orElseThrow(() ->
                NotFoundRestException.with("Crawled page not found with ID: " + id));
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/batch")
    @Operation(summary = "Save or update multiple crawled pages")
    @ApiResponse(responseCode = "201", description = "Crawled pages saved successfully")
    @Override
    public ResponseEntity<RestResponse<List<CrawledPageDto>>> saveOrUpdateBatch(@RequestBody Map<String, List<CrawledPageDto>> requestBody) {
        List<CrawledPageDto> dtoList = requestBody.getOrDefault("pages", new ArrayList<>());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(RestResponse.of(service.saveOrUpdateBatch(dtoList)));
    }

    @PostMapping("/by-ids")
    @Operation(summary = "Get crawled pages by IDs with DOMJson")
    @ApiResponse(responseCode = "200", description = "Crawled pages retrieved successfully")
    @Override
    public RestResponse<List<CrawledPageDto>> findByIds(@RequestBody List<Long> ids) {
        return RestResponse.of(service.findByIds(ids));
    }
}
