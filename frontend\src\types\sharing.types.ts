/**
 * Interface for entities that can be shared
 */
export interface ShareableEntity {
  id: number;
  name: string;
  createdBy: string;
}

/**
 * Interface for a user with whom an entity is shared
 */
export interface SharedUser {
  id: number;
  fullName: string;
  email: string;
  approved: boolean;
}

/**
 * Interface for the request to share an entity with a user
 */
export interface ShareEntityRequest {
  userId: number;
}

/**
 * Interface for the response when sharing an entity
 */
export interface ShareEntityResponse {
  success: boolean;
  message: string;
}
