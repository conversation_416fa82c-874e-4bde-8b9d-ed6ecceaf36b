import React from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';
import RouteParamValidator from '../components/common/RouteParamValidator';
import ProtectedRoute from '../components/ContextApi/ProtectedRoute';
import ForgotPasswordPage from '../components/pages/Auth/ForgotPasswordPage';
import LoginPage from '../components/pages/Auth/LoginPage';
import RegisterPage from '../components/pages/Auth/RegisterPage';
import AdminApprovalPage from '../pages/AdminApproval/AdminApproval';
import ErrorPage from '../pages/Error/ErrorPage';
import { ProjectDashboard } from '../pages/ProjectDashboard/ProjectDashboard';
import ScriptGeneration from '../pages/ScriptGeneration/ScriptGeneration';
import StatusPage from '../pages/StatusPage/StatusPage';
import UserProfilePage from '../pages/UserProfile/UserProfilePage';
import PublicRoute from './../components/ContextApi/PublicRoute';
import OAuthCallbackPage from '../components/pages/Auth/OAuthCallbackPage';
import { Spin } from 'antd';

const ComingSoon: React.FC<{ title: string }> = ({ title }) => (
  <div className="coming-soon-container">
    <h2>{title}</h2>
    <p>This feature is under construction.</p>
  </div>
);

export const ROLES = {
  ADMIN: 'ROLE_ADMIN',
  USER: 'ROLE_USER',
};

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Public routes */}
      <Route element={<PublicRoute />}>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/oauth/callback" element={<OAuthCallbackPage />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
      </Route>

      {/* Protected routes with role-based access */}
      <Route element={<ProtectedRoute unauthorizedRedirect="/unauthorized" />}>
        {/* Default route - accessible to all authenticated users */}
        <Route path="/" element={<Navigate to="/projects" replace />} />

        <Route
          path="/projects"
          element={
            <ProtectedRoute anyRoles={[ROLES.USER, ROLES.ADMIN]}>
              <ProjectDashboard />
            </ProtectedRoute>
          }
        />

        {/* Users - admin only */}
        <Route
          path="/users"
          element={
            <ProtectedRoute roles={[ROLES.ADMIN]}>
              <AdminApprovalPage />
            </ProtectedRoute>
          }
        />

        {/* Profile */}
        <Route path="/profile" element={<UserProfilePage />} />

        {/* Legal pages - public but requires authentication */}
        <Route path="/privacy" element={<ComingSoon title="Privacy Policy Coming Soon" />} />
        <Route path="/terms" element={<ComingSoon title="Terms of Service Coming Soon" />} />
        <Route path="/contact" element={<ComingSoon title="Contact Coming Soon" />} />

        {/* Nested project routes with hierarchical permissions */}
        <Route path="/project/:projectId">
          <Route index element={<Navigate to="script-generation" replace />} />

          <Route
            path="script-generation"
            element={
              <ProtectedRoute anyRoles={[ROLES.USER, ROLES.ADMIN]}>
                <RouteParamValidator
                  requiredParams={['projectId']}
                  component={(resources) =>
                    resources.project ? <ScriptGeneration /> : <Spin />
                  }
                />
              </ProtectedRoute>
            }
          />
        </Route>
      </Route>

      {/* Error routes */}
      <Route path="/error" element={<ErrorPage />} />
      {/* <Route path="/unauthorized" element={<Unauthorized />} /> */}
      <Route path="/404" element={<StatusPage status={404} />} />
      <Route path="/403" element={<StatusPage status={403} />} />
      <Route path="/500" element={<StatusPage status={500} />} />
      {/* Catch-all route to handle undefined routes (404 case) */}
      <Route path="*" element={<StatusPage status={404} />} />
    </Routes>
  );
};

export default AppRoutes;
