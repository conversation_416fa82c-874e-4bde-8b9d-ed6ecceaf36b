package com.enosisbd.app.controller.handler;

import com.enosisbd.app.controller.exception.ProvideValidGitDataException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(value = {ProvideValidGitDataException.class})
    @ResponseBody
    public String handleError(ProvideValidGitDataException ex) {
        return ex.getMessage();
    }

    @ResponseBody
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public String handleError(MissingServletRequestParameterException ex) {
        return ex.getMessage();
    }

}
