import { LockOutlined, MailOutlined, UserOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import AuthenticationService from '../../../services/authentication.service';
import './AuthPage.css';

interface RegisterValues {
  fullName: string;
  email: string;
  password: string;
}

const RegisterPage: React.FC = () => {
  const [serverError, setServerError] = useState('');
  const [formValues, setFormValues] = useState<RegisterValues>({
    fullName: '',
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<Partial<RegisterValues>>({});
  const [successMessage, setSuccessMessage] = useState('');
  const navigate = useNavigate();

  const strongPasswordRegex =
    /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

  const validate = (): boolean => {
    const newErrors: Partial<RegisterValues> = {};

    if (!formValues.fullName.trim()) {
      newErrors.fullName = 'Please enter your full name';
    } else if (formValues.fullName.length < 4) {
      newErrors.fullName = 'Full name must be at least 4 characters';
    } else if (formValues.fullName.length > 50) {
      newErrors.fullName = 'Full name cannot exceed 50 characters';
    }

    if (!formValues.email.trim()) {
      newErrors.email = 'Please enter your email';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formValues.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formValues.password) {
      newErrors.password = 'Please enter your password';
    } else if (!strongPasswordRegex.test(formValues.password)) {
      newErrors.password =
        'Password must contain: 8+ characters, 1 uppercase, 1 lowercase, 1 number, and 1 special character';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validate()) return;

    setServerError('');

    try {
      const response = await AuthenticationService.signUp(formValues);

      if (response?.userId) {
        setSuccessMessage('Account created successfully!');
        setTimeout(() => {
          setSuccessMessage('');
          navigate('/login');
        }, 2000);
      } else {
        throw new Error('Unexpected server response');
      }
    } catch (err: any) {
      const apiMessage =
        err?.response?.data?.message || err?.message || 'Registration failed. Please try again.';
      setServerError(apiMessage);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormValues((prev) => ({ ...prev, [name]: value }));
    setErrors({});
    setServerError('');
  };

  return (
    <div className="auth-container">
      <h3 className="auth-title">Welcome to TAP</h3>
      <p className="auth-subtitle">Sign up to get started with your account</p>

      {successMessage && <div className="alert alert-success">{successMessage}</div>}

      {serverError && <div className="alert alert-error">{serverError}</div>}

      <form onSubmit={handleSubmit} className="auth-form">
        <div className="form-group">
          <label htmlFor="fullName">
            Full Name <span className="required">*</span>
          </label>
          <div className="input-container">
            <UserOutlined className="input-icon" />
            <input
              type="text"
              id="fullName"
              name="fullName"
              value={formValues.fullName}
              onChange={handleChange}
              placeholder="Enter your full name"
            />
          </div>
          {errors.fullName && <div className="auth-error">{errors.fullName}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="email">
            Email Address <span className="required">*</span>
          </label>
          <div className="input-container">
            <MailOutlined className="input-icon" />
            <input
              type="email"
              id="email"
              name="email"
              autoComplete="email"
              value={formValues.email}
              onChange={handleChange}
              placeholder="ex: <EMAIL>"
            />
          </div>
          {errors.email && <div className="auth-error">{errors.email}</div>}
        </div>

        <div className="form-group">
          <label htmlFor="password">
            Password <span className="required">*</span>
          </label>
          <div className="input-container">
            <LockOutlined className="input-icon" />
            <input
              type="password"
              id="password"
              name="password"
              autoComplete="new-password"
              value={formValues.password}
              onChange={handleChange}
              placeholder="Enter your password"
            />
          </div>
          {errors.password && <div className="auth-error">{errors.password}</div>}
        </div>

        <button type="submit" className="btn-primary auth-button">
          Create Account
        </button>
      </form>

      <p className="auth-footer">
        Already have an account?{' '}
        <Link to="/login" className="auth-link">
          Sign in
        </Link>
      </p>
    </div>
  );
};

export default RegisterPage;
