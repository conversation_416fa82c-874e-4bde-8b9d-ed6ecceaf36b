package com.enosisbd.api.server.controller.crawledPage;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.CrawledPageDto;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

public interface CrawledPageApi {
    @PostMapping
    @Operation(summary = "Create a new crawled page")
    @ApiResponse(responseCode = "201", description = "Crawled page created successfully")
    ResponseEntity<RestResponse<CrawledPageDto>> add(@Valid @RequestBody CrawledPageDto dto);

    @GetMapping("/{id}")
    @Operation(summary = "Get crawled page by ID")
    @RequiresEntityAccess(entityType = EntityType.CRAWLED_PAGE)
    RestResponse<CrawledPageDto> getById(@PathVariable Long id);

    @PutMapping("/{id}")
    @Operation(summary = "Update crawled page")
    @RequiresEntityAccess(entityType = EntityType.CRAWLED_PAGE)
    RestResponse<CrawledPageDto> update(@PathVariable Long id, @Valid @RequestBody CrawledPageDto dto);

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete crawled page")
    @ApiResponse(responseCode = "204", description = "Crawled page deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.CRAWLED_PAGE)
    ResponseEntity<Void> delete(@PathVariable Long id);

    @PostMapping("/batch")
    @Operation(summary = "Save or update multiple crawled pages")
    @ApiResponse(responseCode = "201", description = "Crawled pages saved successfully")
    ResponseEntity<RestResponse<List<CrawledPageDto>>> saveOrUpdateBatch(@RequestBody Map<String, List<CrawledPageDto>> requestBody);

    @PostMapping("/by-ids")
    @Operation(summary = "Get crawled pages by IDs with DOMJson")
    @ApiResponse(responseCode = "200", description = "Crawled pages retrieved successfully")
    RestResponse<List<CrawledPageDto>> findByIds(@RequestBody List<Long> ids);
}
