/* ProjectModal specific responsive styles */
.project-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
  padding: 10px;
}

.project-modal-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  width: 100%;
  max-width: 600px;
  max-height: calc(100vh - 30px);
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
}

.project-modal-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.modal-form-row {
  display: flex;
  gap: 5px;
  margin-bottom: 5px;
}

.modal-form-group.half {
  flex: 1;
}

.modal-divider {
  margin: 10px 0;
  border: 0;
  border-top: 1px solid #e8e8e8;
}

/* Style for select dropdown */
.modal-form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
}

/* Style for number input */
.modal-form-group input[type='number'] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.button-with-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .project-modal-overlay {
    padding: 10px;
  }

  .project-modal-container {
    max-width: 100%;
    max-height: calc(100vh - 20px);
  }

  .modal-form-row {
    flex-direction: column;
    gap: 15px;
  }

  .modal-form-group.half {
    flex: none;
  }
}

@media (max-width: 480px) {
  .project-modal-overlay {
    padding: 5px;
  }

  .project-modal-container {
    max-height: calc(100vh - 10px);
  }
}
