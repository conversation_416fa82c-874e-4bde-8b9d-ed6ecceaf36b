package com.enosisbd.api.server.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Optional;
import java.util.UUID;

@Component
@Slf4j
public class RequestLoggingFilter extends OncePerRequestFilter {
    
    private String getClientIP(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null) {
            return request.getRemoteAddr();
        }
        return xfHeader.split(",")[0].trim();
    }

    @Override
    protected void doFilterInternal(
            HttpServletRequest req,
            HttpServletResponse res,
            <PERSON><PERSON><PERSON>hain next
    ) throws ServletException, IOException {
        try {
            // Generate unique request ID
            String requestId = UUID.randomUUID().toString();
            
            // Get client IP
            String clientIP = getClientIP(req);

            // Set values in MDC
            MDC.put("requestId", requestId);
            MDC.put("clientIp", clientIP);

            var queryString =
                    Optional.ofNullable(req.getQueryString())
                            .map(q -> String.format("?%s", q))
                            .orElse("");

            log.info("{} {}{}", req.getMethod(), req.getRequestURI(), queryString);
            log.info("Remote Address: {}", clientIP);

            next.doFilter(req, res);

            log.info("Status: {}, ContentType: {}", res.getStatus(), res.getContentType());
        } finally {
            // Always clear MDC to prevent memory leaks
            MDC.clear();
        }
    }
}
