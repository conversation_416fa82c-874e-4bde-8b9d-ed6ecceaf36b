package com.enosisbd.api.server.config;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
public class TriggramIndexInitializer {

    private static final Logger log = LoggerFactory.getLogger(TriggramIndexInitializer.class);

    @PersistenceContext
    private EntityManager entityManager;

    @EventListener(ApplicationReadyEvent.class)
    @Transactional
    public void createTriggramIndexes() {
        log.info("Creating trigram indexes for text search optimization");
        
        try {
            // Create trigram indexes for User fullName field
            entityManager.createNativeQuery(
                    "CREATE INDEX IF NOT EXISTS idx_users_fullname_trgm ON users USING GIN (full_name gin_trgm_ops)")
                    .executeUpdate();
            
            log.info("Trigram indexes created successfully");
        } catch (Exception e) {
            log.error("Failed to create trigram indexes", e);
        }
    }
}