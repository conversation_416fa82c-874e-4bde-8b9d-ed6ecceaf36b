package com.enosisbd.api.server.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Optional;

/**
 * Module entity representing a module within a project
 */
@Entity
@Table(indexes = {
    @Index(name = "idx_module_project", columnList = "project_id")
})
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Module extends BaseEntity {

    @NotNull(message = "Module name cannot be null")
    @NotBlank(message = "Module name cannot be empty")
    @Size(min = 3, max = 255, message = "Module name must be between 3 and 255 characters")
    @Column(nullable = false)
    private String name;

    @NotNull(message = "Project is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    @OneToMany(mappedBy = "module", cascade = CascadeType.REMOVE, orphanRemoval = true)
    private List<SubModule> subModules;

    public Long getProjectId() {
        return Optional.ofNullable(getProject())
                .map(Project::getId)
                .orElse(null);
    }
}
