package com.enosisbd.api.server.model;

/**
 * Enum representing the different authentication provider types in the system.
 * This provides type safety and centralized definition of provider types.
 */
public enum ProviderType {
    LOCAL("LOCAL"),
    GOOGLE("GOOG<PERSON>");

    private final String name;

    ProviderType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    /**
     * Get a ProviderType from its string name
     * @param name The name of the provider type
     * @return The corresponding ProviderType, or null if not found
     */
    public static ProviderType fromString(String name) {
        if (name == null) {
            return null;
        }
        
        for (ProviderType type : ProviderType.values()) {
            if (type.name.equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }
}