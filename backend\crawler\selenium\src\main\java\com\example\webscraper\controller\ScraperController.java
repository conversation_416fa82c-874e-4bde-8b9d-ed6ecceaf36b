package com.example.webscraper.controller;

import com.example.webscraper.service.ScraperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/scraper")
public class ScraperController {

    @Autowired
    private ScraperService scraperService;

    @GetMapping("/scrape")
    public ResponseEntity<String> scrapeUrl(@RequestParam String url) {
        try {
            String scrapedContent = scraperService.scrapeUrl(url);
            return ResponseEntity.ok(scrapedContent);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Error scraping URL: " + e.getMessage());
        }
    }
}
