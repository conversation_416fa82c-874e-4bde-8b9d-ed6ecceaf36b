import { Language, Platform } from '../types/version.types';

// Define platform-specific configurations
const PLATFORM_CONFIGS = {
  Playwright: {
    assertions: 'playwright assertions',
    testFormat: "test('ID - description', async ({ page }) => { await ID(page); })",
    hooks: 'beforeEach to init, afterEach to quit',
  },
  Selenium: {
    assertions: 'selenium assertions',
    testFormat: "test.describe('ID - description', () => { it('should...', async () => { ... }) })",
    hooks: 'before/after hooks for setup/teardown',
  },
} as const;

// Base prompt template with placeholders
const BASE_AUTOMATION_PROMPT = `Gen raw, executable {language} {platform} automation script using {assertions} from test cases + element JSON (el=elements, t=tag, x=XPath, tx=text≤40 chars, tx*=truncated). Each test must pass/fail.
No markdown, comments, or placeholders
One file, one function per test (named by test ID, described by ID - case)
Each test must:
Use {testFormat}
Use {hooks}
Use XPaths directly from element JSON (ignore rest)
No test case JSON in output
Follow best practices with reusable code
Include all required imports
No global vars/config; define all deps + funcs
Properly initialize the browser, handle SSL certificate errors if needed, clean up resources correctly
Add login helper if needed.`;

export const getSystemPrompt = (platform: Platform = 'Playwright', language: Language = 'TypeScript'): string => {
  const config = PLATFORM_CONFIGS[platform];

  return BASE_AUTOMATION_PROMPT.replace('{language}', language)
    .replace('{platform}', platform)
    .replace('{assertions}', config.assertions)
    .replace('{testFormat}', config.testFormat)
    .replace('{hooks}', config.hooks);
};
