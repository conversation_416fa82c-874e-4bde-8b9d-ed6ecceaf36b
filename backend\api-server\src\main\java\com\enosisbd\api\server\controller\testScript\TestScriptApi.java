package com.enosisbd.api.server.controller.testScript;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.dto.TestScriptDto;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.model.RestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

public interface TestScriptApi {
    @PostMapping
    @Operation(summary = "Create a new test script")
    @ApiResponse(responseCode = "201", description = "Test script created successfully")
    ResponseEntity<RestResponse<TestScriptDto>> add(@Valid @RequestBody TestScriptDto dto);

    @GetMapping("/{id}")
    @Operation(summary = "Get test script by ID")
    @RequiresEntityAccess(entityType = EntityType.TEST_SCRIPT)
    RestResponse<TestScriptDto> getById(@PathVariable Long id);

    @PutMapping("/{id}")
    @Operation(summary = "Update test script")
    @RequiresEntityAccess(entityType = EntityType.TEST_SCRIPT)
    RestResponse<TestScriptDto> update(
            @PathVariable Long id,
            @Valid @RequestBody TestScriptDto dto);

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete test script")
    @ApiResponse(responseCode = "204", description = "Test script deleted successfully")
    @RequiresEntityAccess(entityType = EntityType.TEST_SCRIPT)
    ResponseEntity<Void> delete(@PathVariable Long id);
}
