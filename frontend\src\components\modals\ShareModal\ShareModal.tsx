import { DeleteOutlined, SearchOutlined, UserOutlined } from '@ant-design/icons';
import { Spin, message } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useEffect, useState } from 'react';
import UserService from '../../../services/user.service';
import { ShareableEntityType } from '../../../types/entity.types';
import { ShareableEntity, SharedUser } from '../../../types/sharing.types';
import { User } from '../../../types/user.types';
import { useAuth } from '../../ContextApi/useAuth';
import ModalCloseIcon from '../common/ModalCloseIcon';
import './ShareModal.css';

// Custom error interface for API errors
interface ApiError extends Error {
  status?: number;
}

// Props for the ShareModal component
interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  entityType: ShareableEntityType;
  entity: ShareableEntity;
  sharedUsers: SharedUser[];
  onAddUser: (userId: number) => Promise<void>;
  onRemoveUser: (userId: number) => Promise<void>;
}

/**
 * Modal component for sharing projects or versions with other users
 */
export const ShareModal = ({
  isOpen,
  onClose,
  entityType,
  entity,
  sharedUsers,
  onAddUser,
  onRemoveUser,
}: ShareModalProps) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { roles, user } = useAuth();
  const isAdmin = roles.includes('ROLE_ADMIN');
  const isOwner = entity.createdBy === user?.id;
  const hasRemoveAccess = isAdmin || isOwner;

  // Debounced search function
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      try {
        const users = await UserService.searchUsers(query, entityType, entity.id);

        // Filter out users who already have access, current user, and admin users
        const filteredUsers = users.filter(
          (user) => !sharedUsers?.some((sharedUser) => sharedUser.id === user.id),
        );

        setSearchResults(filteredUsers);
      } catch (error) {
        console.error('Error searching users:', error);
        message.error('Failed to search users');
      } finally {
        setIsSearching(false);
      }
    }, 500),
    [sharedUsers],
  );

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    if (!query.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    debouncedSearch(query);
  };

  // Handle adding a user
  const handleAddUser = async (user: User) => {
    try {
      setIsProcessing(true);
      await onAddUser(user.id);
      setSearchQuery('');
      setSearchResults([]);
      message.success(`${entityType} shared with ${user.fullName}`);
    } catch (error) {
      console.error(`Error sharing ${entityType}:`, error);
      message.error(`Failed to share ${entityType}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle removing a user
  const handleRemoveUser = async (user: SharedUser) => {
    // Check if user has permission (admin or owner)
    if (!hasRemoveAccess) {
      message.error('You do not have permission to remove access');
      return;
    }

    try {
      setIsProcessing(true);
      await onRemoveUser(user.id);
      message.success(`Access removed for ${user.fullName}`);
    } catch (error) {
      console.error(`Error removing access:`, error);

      // Check for specific error messages from the backend
      const apiError = error as ApiError;
      if (apiError.status === 403) {
        message.error('You do not have permission to remove access');
      } else {
        message.error(`Failed to remove access`);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setSearchResults([]);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="share-modal">
        <div className="modal-header">
          <ModalCloseIcon onClose={onClose} />
          <h2>
            Share {entityType}: {entity.name}
          </h2>
        </div>

        <div className="share-modal-content">
          <div className="share-modal-search-section">
            <div className="share-modal-search-input-container">
              <SearchOutlined className="share-modal-search-icon" />
              <input
                type="text"
                placeholder="Search users by name or email..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="share-modal-search-input"
                disabled={isProcessing}
                autoFocus
              />
              {isSearching && <Spin size="small" className="share-modal-search-spinner" />}
            </div>

            {searchQuery && !isSearching && (
              <ul className="share-modal-search-results">
                {searchResults.length > 0 ? (
                  searchResults.map((user) => (
                    <li key={user.id} onClick={() => handleAddUser(user)}>
                      <UserOutlined className="share-modal-user-icon" />
                      <div className="share-modal-user-info">
                        <span className="share-modal-user-name">{user.fullName}</span>
                        <span className="share-modal-user-email">{user.email}</span>
                      </div>
                    </li>
                  ))
                ) : (
                  <li className="share-modal-no-results-item">No users found</li>
                )}
              </ul>
            )}
          </div>

          <div className="share-modal-shared-users-section">
            <h3>Users with access</h3>
            {sharedUsers?.length === 0 ? (
              <div className="share-modal-no-shared-users">
                No users have been granted access yet
              </div>
            ) : (
              <ul className="share-modal-shared-users-list">
                {sharedUsers?.map((user) => (
                  <li key={user.id}>
                    <UserOutlined className="share-modal-user-icon" />
                    <div className="share-modal-user-info">
                      <span className="share-modal-user-name">
                        {user.fullName}
                        {!user.approved && (
                          <span className="share-modal-user-deactivated"> (Deactivated)</span>
                        )}
                      </span>
                      <span className="share-modal-user-email">{user.email}</span>
                    </div>
                    {/* Only show delete button if user has permission (admin or owner) */}
                    {hasRemoveAccess && (
                      <button
                        type="button"
                        className="share-modal-remove-user-button"
                        onClick={() => handleRemoveUser(user)}
                        disabled={isProcessing}
                        title="Remove access"
                      >
                        <DeleteOutlined />
                      </button>
                    )}
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
