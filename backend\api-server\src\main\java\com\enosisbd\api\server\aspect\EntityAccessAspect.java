package com.enosisbd.api.server.aspect;

import com.enosisbd.api.server.annotation.RequiresEntityAccess;
import com.enosisbd.api.server.exception.ForbiddenRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;

@Aspect
@Component
@Order(Ordered.HIGHEST_PRECEDENCE + 1) // Make sure it runs after ExposeInvocationInterceptor
@RequiredArgsConstructor
public class EntityAccessAspect {

    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;

    @Before("@annotation(requiresEntityAccess)")
    public void checkAccess(JoinPoint joinPoint, RequiresEntityAccess requiresEntityAccess) {
        try {
            // Extract method arguments
            Object[] args = joinPoint.getArgs();

            // Get entity type and ID from method arguments
            EntityType entityType;

            // If isDynamic is true, extract entity type from the method arguments
            if (requiresEntityAccess.isDynamic()) {
                entityType = extractEntityType(joinPoint);
                if (entityType == null) {
                    throw new ForbiddenRestException("Entity type not found in method parameters");
                }
            } else {
                entityType = requiresEntityAccess.entityType();
            }

            if (entityType == null) {
                throw new ForbiddenRestException("Invalid entity type");
            }

            Long entityId = extractEntityId(joinPoint, requiresEntityAccess.idParam());

            if (entityId == null) {
                throw new ForbiddenRestException("Entity ID not found in method parameters");
            }

            // Get current user ID
            Long currentUserId = authorizationService.getCurrentUserId();
            if (currentUserId == null) {
                throw new ForbiddenRestException("User not authenticated");
            }

            // Check if user has access
            if (!entitySharingService.hasAccess(currentUserId, entityType, entityId)) {
                throw new ForbiddenRestException("You don't have access to this " + entityType.getName().toLowerCase());
            }
        } catch (Exception e) {
            if (e instanceof ForbiddenRestException) {
                throw e;
            }
            throw new ForbiddenRestException("Access check failed: " + e.getMessage());
        }
    }

    private Long extractEntityId(JoinPoint joinPoint, String idParam) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        // First try to find parameter by name
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].getName().equals(idParam) && args[i] instanceof Long) {
                return (Long) args[i];
            }
        }

        // If not found by name, try to find by type (first Long parameter)
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof Long) {
                return (Long) args[i];
            }
        }

        return null;
    }

    private EntityType extractEntityType(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        // Try to find a parameter named "entityType"
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].getName().equals("entityType")) {
                if (args[i] instanceof EntityType) {
                    return (EntityType) args[i];
                } else if (args[i] instanceof String) {
                    return EntityType.fromString((String) args[i]);
                }
            }
        }

        return null;
    }
}
