package com.enosisbd.api.server.controller.jwks;

import com.enosisbd.api.server.security.SecurityConfig;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@Tag(name = "JWKS Controller")
@RequestMapping("/.well-known")
@RequiredArgsConstructor
public class JwksController {

    private final SecurityConfig securityConfig;

    @GetMapping("/jwks")
    public Map<String, Object> getJwks() {
        List<JWK> jwks = securityConfig.jwkSet().toPublicJWKSet().getKeys();
        JWKSet jwkSet = new JWKSet(jwks);
        return jwkSet.toJSONObject();
    }
}