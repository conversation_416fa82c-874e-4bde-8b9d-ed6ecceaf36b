.report-button-container {
  display: flex;
  gap: 0.5rem;
  opacity: 1;
  transition: opacity 0.2s;
  flex-direction: row;
}

.report-button-container .action-button {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed var(--border-color);
  box-shadow: var(--shadow-sm);
  color: var(--text-secondary);
  transition: all 0.2s;
}

.report-button-container .action-button:hover {
  transform: translateY(-2px);
}

.report-button-container .action-button.report:hover {
  background-color: var(--primary-black);
  color: white;
  border-color: var(--primary-black);
}
