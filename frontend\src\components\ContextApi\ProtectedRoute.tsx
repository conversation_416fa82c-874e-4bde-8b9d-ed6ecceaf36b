import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from './useAuth';

interface ProtectedRouteProps {
  roles?: string[];     
  anyRoles?: string[];
  unauthorizedRedirect?: string;
  children?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  roles,
  anyRoles,
  unauthorizedRedirect = '/403',
  children,
}) => {
  const { isAuthenticated, isLoading, roles: userRoles } = useAuth();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check if user has ALL required roles
  if (roles && !roles.every(role => userRoles.includes(role))) {
    return <Navigate to={unauthorizedRedirect} replace />;
  }

  // Check if user has AT LEAST ONE of the anyRoles
  if (anyRoles && !anyRoles.some(role => userRoles.includes(role))) {
    return <Navigate to={unauthorizedRedirect} replace />;
  }

  // Render children if provided, else fallback to <Outlet />
  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute;
