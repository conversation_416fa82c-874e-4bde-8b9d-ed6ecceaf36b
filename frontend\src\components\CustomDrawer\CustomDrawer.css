/* CustomDrawer styles */
.custom-drawer .ant-drawer-content-wrapper {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

.custom-drawer .ant-drawer-header {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.custom-drawer .ant-drawer-title {
  font-size: var(--font-medium);
  color: #343a40;
}

.custom-drawer .ant-drawer-close {
  color: #6c757d;
}

.custom-drawer .ant-drawer-close:hover {
  color: #343a40;
}

.custom-drawer .ant-drawer-body {
  padding: 12px 8px 12px 12px !important;
  display: flex;
  flex-direction: column;
  height: calc(100% - 55px); /* Header height is typically 55px */
}

.drawer-content {
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 0.9rem;
  overflow-y: auto;
  flex: 1;
}

/* Toggle button styling */
.drawer-toggle-button {
  position: fixed !important;
  top: 50% !important;
  transform: translateY(-50%);
  z-index: 99;
}

/* Styling for toggle button when it has unaccepted content */
.drawer-toggle-button.has-content {
  animation: pulse 1.5s infinite;
  box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
}

@keyframes pulse {
  0% {
    transform: translateY(-50%) scale(0.95);
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }

  70% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }

  100% {
    transform: translateY(-50%) scale(0.95);
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.drawer-toggle-button.right {
  right: 0 !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.drawer-toggle-button.left {
  left: 0 !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

/* Make sure drawer appears on top of other elements */
.ant-drawer {
  z-index: 1050;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .custom-drawer .ant-drawer-content-wrapper {
    width: 75% !important;
  }
}

/* Streaming content styling */
.streaming-content {
  white-space: pre-wrap;
  font-family: monospace;
  font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 12px;
  overflow-y: auto;
  flex: 1;
}
