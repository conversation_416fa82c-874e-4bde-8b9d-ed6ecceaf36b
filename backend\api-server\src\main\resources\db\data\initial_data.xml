<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!-- Load initial users data -->
    <changeSet id="load-users-from-csv" author="system" context="dev">
        <loadData relativeToChangelogFile="true" file="users.csv" tableName="users" separator=","/>
    </changeSet>

    <!-- Load initial roles data -->
    <changeSet id="load-roles-from-csv" author="system" context="dev">
        <loadData relativeToChangelogFile="true" file="roles.csv" tableName="roles" separator=","/>
    </changeSet>

    <!-- Load initial user-role mappings -->
    <changeSet id="load-users-roles-from-csv" author="system" context="dev">
        <loadData relativeToChangelogFile="true" file="users_roles.csv" tableName="users_roles" separator=","/>
    </changeSet>
    
    <!-- Load sharable entity types -->
    <changeSet id="load-sharable-entity-types" author="system" context="dev">
        <sql> INSERT INTO sharable_entity_types (name, description, is_active, created_at,
            updated_at, created_by, last_modified_by) VALUES ('PROJECT', 'Project entity', true,
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'SYSTEM', 'SYSTEM'); </sql>
    </changeSet>
</databaseChangeLog>