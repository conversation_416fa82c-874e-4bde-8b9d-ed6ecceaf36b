package com.enosisbd.api.server.controller.authentication;

import com.enosisbd.api.server.controller.authentication.exceptions.UserIsNotActiveException;
import com.enosisbd.api.server.controller.authentication.exceptions.UsernameAlreadyExistsException;
import com.enosisbd.api.server.dto.AuthenticationDTO.*;
import com.enosisbd.api.server.service.authentication.AuthenticationService;
import com.nimbusds.jose.JOSEException;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Log4j2
@RestController
@Tag(name = "Authentication Operations")
@RequestMapping("/api/auth")
public class AuthenticationController implements AuthenticationApi {

    private final AuthenticationService authenticationService;

    public AuthenticationController(AuthenticationService authenticationService) {
        this.authenticationService = authenticationService;
    }

    @Override
    @PostMapping("/signIn")
    public ResponseEntity<SignInResponseDTO> signIn(@Valid @RequestBody SignInDTO signInDTO) throws UserIsNotActiveException {
        return this.authenticationService.signIn(signInDTO);
    }

    @Override
    @PostMapping("/signUp")
    public ResponseEntity<SignUpResponseDTO> signUp(@Valid @RequestBody SignUpDTO signUpDTO) throws UsernameAlreadyExistsException {
        return this.authenticationService.signUp(signUpDTO);
    }

    @PostMapping("/refreshToken")
    @Override
    public ResponseEntity<RefreshTokenResponseDTO> refreshToken(@Valid @RequestBody RefreshTokenDTO refreshTokenDTO) throws UserIsNotActiveException, JOSEException {
        return this.authenticationService.refreshToken(refreshTokenDTO);
    }

    @Override
    @PostMapping("/forgotPassword")
    public ResponseEntity<ForgotPasswordResponseDTO> forgotPassword(@Valid @RequestBody ForgotPasswordDTO forgotPasswordDTO) {
        return this.authenticationService.forgotPassword(forgotPasswordDTO);
    }

    @Override
    @PostMapping("/resetPassword")
    public ResponseEntity<ResetPasswordResponseDTO> resetPassword(@Valid @RequestBody ResetPasswordDTO resetPasswordDTO) {
        return this.authenticationService.resetPassword(resetPasswordDTO);
    }
}
