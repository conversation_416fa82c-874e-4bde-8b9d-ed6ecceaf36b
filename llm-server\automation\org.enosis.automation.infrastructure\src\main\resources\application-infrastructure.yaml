spring:
  # Ollama Configuration
  ai:
    ollama:
      base-url: http://192.168.0.252:11434

server:
  tomcat:
    connection-timeout: 300s
    keep-alive-timeout: 300s
  servlet:
    session:
      timeout: 300s

springdoc:
  api-docs:
    groups:
      enabled: true
    version: openapi_3_0
  swagger-ui:
    path: /swagger-ui/index.html


management:
  endpoints:
    web:
      exposure:
        include: '*'

configuration:
  temp: 0.5          # Temperature controls the randomness of the output. Lower values (e.g., 0.5) make the output more deterministic and focused.
  topP: 0.5          # Top-P (nucleus sampling) controls the diversity of the output. Lower values (e.g., 0.8) focus on high-probability tokens, reducing irrelevant outputs.
  numKeep: 24        # Number of tokens to retain from the input prompt. This ensures the model retains sufficient context for coherent responses.
  seed: 42           # Random seed for reproducibility. A fixed seed ensures consistent outputs across multiple runs.
  numPredict: 2000   # Maximum number of tokens to generate. Lower values (e.g., 500) prevent overly long outputs, which is useful for concise responses.


llm-lingua:
  llmlingua-2-xlm-roberta-large-meetingbank:
    compression:
      rate: 0.8 # Prompt Compression Rate