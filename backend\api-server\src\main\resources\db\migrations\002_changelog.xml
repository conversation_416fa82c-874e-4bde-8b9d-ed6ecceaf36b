<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="mohibur (generated)" id="1747367854469-15">
        <dropForeignKeyConstraint baseTableName="test_suite" constraintName="FK3wq5c8i0ygh18lxpmk04p5b4n"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-16">
        <dropForeignKeyConstraint baseTableName="test_script" constraintName="FK5ns6tem7vclikpotk12ggg5sh"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-17">
        <dropForeignKeyConstraint baseTableName="version" constraintName="FK5q7csydn4alo2pf0bbv74g9ko"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-18">
        <dropForeignKeyConstraint baseTableName="test_case" constraintName="FKhjc8d84sw3w3oe3ccnnlj3rde"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-19">
        <dropForeignKeyConstraint baseTableName="crawled_page" constraintName="FKmavq1eqfpuw8umgs0spg86l3p"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-3">
        <createTable tableName="module">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="modulePK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="project_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-4">
        <createTable tableName="sub_module">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="sub_modulePK"/>
            </column>
            <column name="created_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="created_by" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="updated_at" type="TIMESTAMP(6) WITHOUT TIME ZONE"/>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="module_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-5">
        <addColumn tableName="crawled_page">
            <column name="project_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-6">
        <addColumn tableName="test_script">
            <column name="submodule_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-7">
        <createIndex indexName="idx_crawled_page_project" tableName="crawled_page">
            <column name="project_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-8">
        <createIndex indexName="idx_module_project" tableName="module">
            <column name="project_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-9">
        <createIndex indexName="idx_submodule_module" tableName="sub_module">
            <column name="module_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-10">
        <createIndex indexName="idx_test_script_submodule" tableName="test_script">
            <column name="submodule_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-11">
        <addForeignKeyConstraint baseColumnNames="submodule_id" baseTableName="test_script" constraintName="FK2nt7tjesiti46i8apk73xpuit" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="sub_module" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-12">
        <addForeignKeyConstraint baseColumnNames="project_id" baseTableName="module" constraintName="FKcjhgsdebgmotrcyx8ws2mv3ot" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="project" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-13">
        <addForeignKeyConstraint baseColumnNames="project_id" baseTableName="crawled_page" constraintName="FKs0bruihnpyu618d0v01vkfw0m" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="project" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-14">
        <addForeignKeyConstraint baseColumnNames="module_id" baseTableName="sub_module" constraintName="FKsiy8j1vihx4qrh9ap5mdfkn13" deferrable="false" initiallyDeferred="false" referencedColumnNames="id" referencedTableName="module" validate="true"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-20">
        <dropUniqueConstraint constraintName="uc_test_scripttest_suite_id_col" tableName="test_script"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-21">
        <dropTable tableName="test_case"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-22">
        <dropTable tableName="test_suite"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-23">
        <dropTable tableName="version"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-24">
        <dropColumn columnName="git_platform_email" tableName="project"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-25">
        <dropColumn columnName="repository_url" tableName="project"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-26">
        <dropColumn columnName="test_suite_id" tableName="crawled_page"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-27">
        <dropColumn columnName="test_suite_id" tableName="test_script"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-1">
        <dropUniqueConstraint constraintName="UC_USERSEMAIL_COL" tableName="users"/>
    </changeSet>
    <changeSet author="mohibur (generated)" id="1747367854469-2">
        <addUniqueConstraint columnNames="email" constraintName="UC_USERSEMAIL_COL" tableName="users"/>
    </changeSet>
</databaseChangeLog>
