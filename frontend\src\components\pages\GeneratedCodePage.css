/* Shared styles for component pages */
.component-page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 0.9rem;
  padding: 0.5rem;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
}

.component-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.component-content {
  flex: 1;
  overflow: auto;
}

/* Code editor specific styles */
.code-container {
  height: 350px;
}

.button-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

.run-button {
  position: absolute;
  bottom: 12px;
  right: 12px;
  z-index: 10;
  padding: 6px 14px;
  font-size: var(--font-regular);
  background-color: #f5f5f5;
  border: 1px solid var(--border-color);
  color: #555;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.run-button:hover {
  background-color: #e0e0e0;
}

.run-button:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
  opacity: 0.7;
}

.output-container {
  margin-top: 12px;
}

.output-container h3 {
  font-size: 0.95rem;
  margin: 0 0 8px 0;
}

.output-container pre {
  background-color: #1e1e1e;
  color: #ffffff;
  padding: 12px;
  border-radius: 4px;
  overflow: auto;
  max-height: 250px;
  font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 12px;
  text-wrap: auto;
}

/* Test cases specific styles */
.test-cases-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-case-view h3 {
  font-size: 0.95rem;
  margin: 0 0 8px 0;
}

.test-case-view ul {
  margin: 0 0 8px 0;
  padding-left: 20px;
  font-size: var(--font-regular);
}

.test-case-view p {
  font-size: var(--font-regular);
  margin: 0;
}

.error-message {
  margin-top: 12px;
  padding: 10px;
  background-color: #f8d7da;
  border: 1px solid #f5c2c7;
  color: #842029;
  border-radius: 4px;
}
