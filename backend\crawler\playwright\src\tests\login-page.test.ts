import { expect } from 'chai';
import { <PERSON><PERSON><PERSON>, <PERSON>uilder, By, until, WebDriver } from 'selenium-webdriver';

const CONSTANTS = {
    urls: {
        login: 'http://localhost:3000/login',
        dashboard: 'http://localhost:3000/dashboard'
    },
    selectors: {
        username: '#username',
        password: '#password',
        loginButton: '#login-btn',
        error: '.error'
    },
    credentials: {
        valid: {
            username: 'admin',
            password: 'admin'
        },
        invalid: {
            username: 'invalid',
            password: 'invalid'
        }
    },
    messages: {
        invalidCredential: 'Invalid username or password',
        emptyUsername: 'Username is required',
        emptyPassword: 'Password is required'
    }
}

describe('Login Page Test', function () {
    let driver: WebDriver;

    beforeEach(async function () {
        // Initialize the WebDriver (assuming ChromeDriver is installed and in PATH)
        driver = await new Builder()
            .forBrowser(Browser.CHROME)
            .usingServer("http://localhost:7070")
            .build();
    });

    it('TC01 - should navigate to dashboard after successful login', async function () {
        // Navigate to login page
        await driver.get(CONSTANTS.urls.login);

        // Find the username input and enter a query
        const usernameInput = await driver.findElement(By.css(CONSTANTS.selectors.username));
        await usernameInput.sendKeys(CONSTANTS.credentials.valid.username);

        // Find the password input and enter a query
        const passwordInput = await driver.findElement(By.css(CONSTANTS.selectors.password));
        await passwordInput.sendKeys(CONSTANTS.credentials.valid.password);

        // Find the login button and click it
        const loginButton = await driver.findElement(By.css(CONSTANTS.selectors.loginButton));
        await loginButton.click();
        
        // wait for dashboard
        await driver.wait(until.urlContains(CONSTANTS.urls.dashboard), 10000);

        // check if url is /dashboard
        const url = await driver.getCurrentUrl();
        expect(url).to.equal(CONSTANTS.urls.dashboard);
    });

    it('TC02 - should show error message after invalid credentials', async function () {
        // Navigate to login page
        await driver.get(CONSTANTS.urls.login);

        // Find the username input and enter a query
        const usernameInput = await driver.findElement(By.css(CONSTANTS.selectors.username));
        await usernameInput.sendKeys(CONSTANTS.credentials.invalid.username);

        // Find the password input and enter a query
        const passwordInput = await driver.findElement(By.css(CONSTANTS.selectors.password));
        await passwordInput.sendKeys(CONSTANTS.credentials.invalid.password);

        // Find the login button and click it
        const loginButton = await driver.findElement(By.css(CONSTANTS.selectors.loginButton));
        await loginButton.click();

        // wait for error message
        await driver.wait(until.elementLocated(By.css(CONSTANTS.selectors.error)), 10000);

        // check if error message is displayed
        const error = await driver.findElement(By.css(CONSTANTS.selectors.error)).getText();
        // check error message
        expect(error).to.have.string(CONSTANTS.messages.invalidCredential);
    });

    it('TC03 - should show error message when username is empty', async function () {
        // Navigate to login page
        await driver.get(CONSTANTS.urls.login);

        // Find the password input and enter a query
        const passwordInput = await driver.findElement(By.css(CONSTANTS.selectors.password));
        await passwordInput.sendKeys(CONSTANTS.credentials.valid.password);

        // Find the login button and click it
        const loginButton = await driver.findElement(By.css(CONSTANTS.selectors.loginButton));
        await loginButton.click();

        // wait for error message
        await driver.wait(until.elementLocated(By.css(CONSTANTS.selectors.error)), 10000);

        // check if error message is displayed
        const error = await driver.findElement(By.css(CONSTANTS.selectors.error)).getText();
        // check error message
        expect(error).to.have.string(CONSTANTS.messages.emptyUsername);
    });

    it('TC04 - should show error message when password is empty', async function () {
        // Navigate to login page
        await driver.get(CONSTANTS.urls.login);

        // Find the username input and enter a query
        const usernameInput = await driver.findElement(By.css(CONSTANTS.selectors.username));
        await usernameInput.sendKeys(CONSTANTS.credentials.valid.username);

        // Find the login button and click it
        const loginButton = await driver.findElement(By.css(CONSTANTS.selectors.loginButton));
        await loginButton.click();

        // wait for error message
        await driver.wait(until.elementLocated(By.css(CONSTANTS.selectors.error)), 10000);

        // check if error message is displayed
        const error = await driver.findElement(By.css(CONSTANTS.selectors.error)).getText();
        // check error message
        expect(error).to.have.string(CONSTANTS.messages.emptyPassword);
    });

    it('TC05 - should show error message when both username and password are empty', async function () {
        // Navigate to login page
        await driver.get(CONSTANTS.urls.login);

        // Find the login button and click it
        const loginButton = await driver.findElement(By.css(CONSTANTS.selectors.loginButton));
        await loginButton.click();

        // wait for error message
        await driver.wait(until.elementLocated(By.css(CONSTANTS.selectors.error)), 10000);

        // check if error message is displayed
        const error = await driver.findElement(By.css(CONSTANTS.selectors.error)).getText();
        // check error message
        expect(error).to.have.string(CONSTANTS.messages.emptyUsername);
    });

    afterEach(async function () {
        // Close the browser
        await driver.quit();
    });
});