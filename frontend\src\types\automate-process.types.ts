// Types for the AutomateProcess feature
export type AuthType = 'none' | 'credentials' | 'bearer' | 'cookies' | 'localStorage';

export type SheetTestCase = {
  caseId: string;
  description: string;
  expectedResult: string;
};

export type SheetTestCasesResponse = {
  testCases: SheetTestCase[];
};

export type LoginCredentials = {
  url: string;
  username: string;
  password: string;
  usernameSelector: string;
  passwordSelector: string;
  submitSelector: string;
};

export type Cookie = {
  name: string;
  value: string;
  domain?: string;
  path?: string;
};

export type TestScript = {
  id: number;
  createdAt: string;
  updatedAt: string;
  code: string;
};