package com.example.webscraper.service;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.springframework.stereotype.Service;

@Service
public class ScraperService {
    
    public String scrapeUrl(String url) {
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless"); // Run in headless mode
        options.addArguments("--disable-gpu");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        
        WebDriver driver = new ChromeDriver(options);
        String pageSource = "";
        
        try {
            driver.get(url);
            pageSource = driver.getPageSource();
        } catch (Exception e) {
            throw new RuntimeException("Failed to scrape URL: " + url, e);
        } finally {
            driver.quit();
        }
        
        return pageSource;
    }
}
