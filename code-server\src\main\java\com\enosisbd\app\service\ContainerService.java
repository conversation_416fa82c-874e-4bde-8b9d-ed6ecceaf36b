package com.enosisbd.app.service;

import com.enosisbd.app.util.GitProviderUtils.GitProvider;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import org.springframework.http.ResponseEntity;

public interface ContainerService {

  /**
   * Start a container with the specified repository
   *
   * @param uuid Unique identifier for the container
   * @param gitRepositoryUrl Git repository URL
   * @param gitUserEmail User email for Git configuration
   * @param accessToken OAuth2 access token
   * @return Response with container status
   */
  default ResponseEntity<String> start(String uuid, String gitRepositoryUrl, String gitUserEmail,
      String accessToken) {
    return start(uuid, gitRepositoryUrl, gitUserEmail, accessToken, "github");
  }

  /**
   * Start a container with the specified repository
   *
   * @param uuid Unique identifier for the container
   * @param gitRepositoryUrl Git repository URL
   * @param gitUserEmail User email for Git configuration
   * @param accessToken OAuth2 access token
   * @param provider Git provider (github or bitbucket)
   * @return Response with container status
   */
  ResponseEntity<String> start(String uuid, String gitRepositoryUrl, String gitUserEmail,
      String accessToken, String provider);

  /**
   * Stop a container
   *
   * @param username Username
   * @param repository Repository name
   * @return Response with stop status
   */
  ResponseEntity<String> stop(String username, String repository);

  /**
   * Validate if the Git token has access to the repository
   *
   * @param gitRepositoryUrl Git repository URL
   * @param accessToken OAuth2 access token
   * @param provider Git provider (github or bitbucket)
   * @return true if token is valid, false otherwise
   */
  default boolean isGitTokenValid(String gitRepositoryUrl, String accessToken, String provider) {
    GitProvider gitProvider = GitProvider.GITHUB;
    if ("bitbucket".equalsIgnoreCase(provider)) {
      gitProvider = GitProvider.BITBUCKET;
    }

    try {
      if (gitProvider == GitProvider.GITHUB) {
        URL url = new URL(gitRepositoryUrl.replace("github.com", "api.github.com/repos"));
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setRequestProperty("Authorization", "token " + accessToken);
        connection.setRequestProperty("Accept", "application/vnd.github.v3+json");
        connection.setConnectTimeout(3000);
        connection.setReadTimeout(3000);
        int responseCode = connection.getResponseCode();
        return responseCode == 200 || responseCode == 204;
      } else if (gitProvider == GitProvider.BITBUCKET) {
        // Extract owner and repo name from URL
        String[] parts = gitRepositoryUrl.split("/");
        String owner = parts[parts.length - 2];
        String repo = parts[parts.length - 1].replace(".git", "");

        URL url = new URL("https://api.bitbucket.org/2.0/repositories/" + owner + "/" + repo);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setRequestProperty("Authorization", "Bearer " + accessToken);
        connection.setConnectTimeout(3000);
        connection.setReadTimeout(3000);
        int responseCode = connection.getResponseCode();
        return responseCode == 200 || responseCode == 204;
      }
      return false;
    } catch (IOException e) {
      return false;
    }
  }

  /**
   * Backward compatibility method
   */
  default boolean isGitTokenValid(String gitRepositoryUrl, String accessToken) {
    return isGitTokenValid(gitRepositoryUrl, accessToken, "github");
  }
}
