package com.enosisbd.api.server.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for representing a submodule with its row range
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubModuleRangeDto {
    private String name;
    private String range; // e.g., "1-1", "11-13", "14-18"
    private Integer startRow;
    private Integer endRow;
}
