package com.enosisbd.app.security;

import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class CustomOAuth2UserService extends DefaultOAuth2UserService {
  @Override
  public OAuth2User loadUser(OAuth2UserRequest userRequest) {
    OAuth2User oauth2User = super.loadUser(userRequest);
    OAuth2AccessToken accessToken = userRequest.getAccessToken();

    Map<String, Object> attributes = new HashMap<>(oauth2User.getAttributes());
    attributes.put("access_token", accessToken.getTokenValue());

    // Store the client registration ID (github or bitbucket)
    String registrationId = userRequest.getClientRegistration().getRegistrationId();
    attributes.put("clientRegistrationId", registrationId);

    return new CustomOAuth2User(oauth2User, attributes);
  }
}