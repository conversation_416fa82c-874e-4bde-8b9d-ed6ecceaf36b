import { LLMRequestParams } from '../types/llm.types';

export const prepareLLMRequest = (data: string | LLMRequestParams) => {
  // Check if data is a string or an object with data, language, and platform
  if (typeof data === 'string') {
    return {
      systemCommand: '',
      question: data,
      compressionEnabled: true,
    };
  } else {
    // If it's an object, extract the data, language, and platform
    const { data: question, systemCommand } = data;
    return {
      systemCommand,
      question,
      compressionEnabled: true,
    };
  }
};

export const filteroutTestScriptFromLLMResponse = (input: string) => {
  // Match the code block enclosed in triple backticks with TypeScript (```typescript ... ```)
  const match = input.match(/```typescript\s*([\s\S]*?)\s*```/);

  // If a match is found, return the extracted code; otherwise, return the original text
  return match ? match[1].trim() : input.trim();
};

export const isObjectEmpty = (obj: any): boolean => {
  if (obj == null || typeof obj !== 'object') return true;

  return Object.keys(obj).every((key) => {
    const value = obj[key];

    if (value == null || value == undefined) return true;

    if (typeof value === 'string') return value.trim() === '';

    if (Array.isArray(value)) return value.length === 0;

    if (typeof value === 'object') return isObjectEmpty(value);

    return false;
  });
};

export const hasAnyEmptyField = (obj: any): boolean => {
  if (obj == null || typeof obj !== 'object') return true;

  return Object.keys(obj).some((key) => {
    const value = obj[key];

    if (value == null) return true; // null or undefined

    if (typeof value === 'string') return value.trim() === '';

    if (Array.isArray(value)) return value.length === 0;

    if (typeof value === 'object') return hasAnyEmptyField(value);

    return false;
  });
};
