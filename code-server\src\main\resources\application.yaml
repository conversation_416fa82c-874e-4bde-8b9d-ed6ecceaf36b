spring:
  application:
    name: code-server
  main:
    banner-mode: off
  profiles:
    active:
      - ${SPRING_PROFILE_ACTIVE:dev}
      - infra
  security:
    oauth2:
      client:
        registration:
          github:
            client-id: ${code-server.github-oauth2.client-id}
            client-secret: ${code-server.github-oauth2.client-secret}
            scope: repo, read:email
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
            authorization-grant-type: authorization_code
          bitbucket:
            client-id: CRC7mt5tc2JSgtvjfV
            client-secret: Zm3hMwkqtQg2FrjX2p8bFbQdhvjCYkNu
            scope: account, repository
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
            authorization-grant-type: authorization_code
        provider:
          github:
            authorization-uri: https://github.com/login/oauth/authorize
            token-uri: https://github.com/login/oauth/access_token
            user-info-uri: https://api.github.com/user
          bitbucket:
            authorization-uri: https://bitbucket.org/site/oauth2/authorize
            token-uri: https://bitbucket.org/site/oauth2/access_token
            user-info-uri: https://api.bitbucket.org/2.0/user
            user-name-attribute: username
server:
  port: 8081

app:
  cors:
    allowed-origins: http://localhost:5173
  scripts:
    cleanup-path: /usr/local/environment/clean_repos.sh

logging:
  # File configuration
  file:
    name: logs/${spring.application.name}-error.log
  logback:
    rollingpolicy:
      file-name-pattern: logs/${spring.application.name}-error-%d{yyyy-MM-dd}.log
      max-history: 10
      clean-history-on-start: true

  pattern:
    # Detailed console pattern with colored output, IP and request tracking
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%-5level) [%clr(%thread){magenta}] %clr(%-40.40logger{39}){cyan} [IP: %X{clientIp:-UNKNOWN}] [ReqID: %X{requestId:-SYSTEM}] : %msg%n"
    # Comprehensive file pattern for error investigation
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{40} [IP: %X{clientIp:-UNKNOWN}] [ReqID: %X{requestId:-SYSTEM}] : %msg%n"

  # Log level configuration
  level:
    root: INFO

    com.enosisbd.app:
      console: INFO
      file: ERROR

    org.springframework:
      console: WARN
      file: ERROR
    org.hibernate:
      console: WARN
      file: ERROR
    org.apache:
      console: WARN
      file: ERROR
