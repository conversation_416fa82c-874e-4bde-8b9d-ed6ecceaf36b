package com.enosisbd.api.server.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "entity_access",
        uniqueConstraints = @UniqueConstraint(columnNames = {"shared_with_user_id", "entity_type_id", "entity_id"}),
        indexes = {
                @Index(name = "idx_entity_access_entity", columnList = "entity_type_id, entity_id"),
                @Index(name = "idx_entity_access_is_inherited", columnList = "shared_with_user_id, is_inherited")
        })
@Getter
@Setter
public class EntityAccess extends BaseEntity {

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "entity_type_id", nullable = false)
    private SharableEntityType entityType;

    @Column(name = "entity_id", nullable = false)
    private Long entityId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shared_by_user_id", nullable = false)
    private User sharedByUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shared_with_user_id", nullable = false)
    private User sharedWithUser;

    /**
     * Indicates whether this access was granted directly or through inheritance
     * For example, when sharing a version, the parent project access is marked as inherited
     */
    @Column(name = "is_inherited", nullable = false)
    private Boolean isInherited = false;
}
