server {
    listen 5173;
    server_name localhost;

    # Root directory where the built React app is located
    root /usr/share/nginx/html;
    index index.html;

    # Serve the static files directly
    location / {
        try_files $uri /index.html;
    }

    # Enable Gzip compression for better performance
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_proxied any;
    gzip_vary on;

    # Cache static files for better performance
    location ~* \.(?:css|js|json|woff|woff2|ttf|eot|svg|png|jpg|jpeg|gif|ico)$ {
        expires 1y;
        access_log off;
    }

    # Fallback to index.html for routing to React app (client-side routing)
    error_page  404  /index.html;
}
