import React, { useEffect, useMemo, useState } from 'react';
import {
  AuthConfig,
  Cookie,
  LoginCredentials,
} from '../../../types/web-crawler.types';
import { AuthType } from '../../../types/automate-process.types';
import './AuthForm.css';

interface AuthFormProps {
  authType: AuthType;
  setAuthType: (type: AuthType) => void;
  onAuthChange?: (authConfig: AuthConfig) => void;
}

export const AuthForm: React.FC<AuthFormProps> = ({ authType, setAuthType, onAuthChange }) => {
  const [credentials, setCredentials] = useState<LoginCredentials>({
    url: '',
    username: '',
    password: '',
    usernameSelector: '',
    passwordSelector: '',
    submitSelector: '',
  });
  const [bearerToken, setBearerToken] = useState('');
  const [localStorage, setLocalStorage] = useState<{ key: string; value: string }[]>([]);
  const [cookies, setCookies] = useState<Cookie[]>([]);

  // Create a memoized auth config object to prevent unnecessary re-renders
  const authConfig = useMemo<AuthConfig>(
    () => ({
      credentials: authType === 'credentials' ? credentials : undefined,
      bearerToken: authType === 'bearer' ? bearerToken : undefined,
      localStorage: authType === 'localStorage' ? localStorage : undefined,
      cookies: authType === 'cookies' ? cookies : undefined,
    }),
    [authType, credentials, bearerToken, localStorage, cookies],
  );

  // Update parent component when auth config changes
  useEffect(() => {
    if (onAuthChange) {
      onAuthChange(authConfig);
    }
  }, [authConfig, onAuthChange]);

  // Reset form data when auth type changes
  useEffect(() => {
    if (authType === 'none') {
      setCredentials({
        url: '',
        username: '',
        password: '',
        usernameSelector: '',
        passwordSelector: '',
        submitSelector: '',
      });
      setBearerToken('');
      setLocalStorage([]);
      setCookies([]);
    }
  }, [authType]);

  const handleAddLocalStorage = () => {
    setLocalStorage([...localStorage, { key: '', value: '' }]);
  };

  const handleUpdateLocalStorage = (index: number, field: 'key' | 'value', value: string) => {
    const newLocalStorage = [...localStorage];
    newLocalStorage[index] = { ...newLocalStorage[index], [field]: value };
    setLocalStorage(newLocalStorage);
  };

  const handleRemoveLocalStorage = (index: number) => {
    setLocalStorage(localStorage.filter((_, i) => i !== index));
  };

  const handleAddCookie = () => {
    setCookies([
      ...cookies,
      {
        name: '',
        value: '',
        url: '',
      },
    ]);
  };

  const handleUpdateCookie = (index: number, field: keyof Cookie, value: string) => {
    const newCookies = [...cookies];
    newCookies[index] = { ...newCookies[index], [field]: value };
    setCookies(newCookies);
  };

  const handleRemoveCookie = (index: number) => {
    setCookies(cookies.filter((_, i) => i !== index));
  };

  // Add this helper function to check if a local storage item is complete
  const isLocalStorageItemComplete = (item: { key: string; value: string }): boolean => {
    return !!(item.key?.trim() && item.value?.trim());
  };

  // Add this function to check if all existing local storage items are complete
  const canAddLocalStorageItem = (): boolean => {
    return localStorage.length === 0 || localStorage.every(isLocalStorageItemComplete);
  };

  // Add helper function to check if a cookie is complete
  const isCookieComplete = (cookie: Cookie): boolean => {
    return !!(cookie.name?.trim() && cookie.value?.trim() && cookie.url?.trim());
  };

  // Add function to check if all existing cookies are complete
  const canAddCookie = (): boolean => {
    return cookies.length === 0 || cookies.every(isCookieComplete);
  };

  return (
    <div className="auth-section">
      <h3>Authentication Configuration</h3>

      <div className="auth-type-selector">
        <label htmlFor="authType">
          Authentication Method <span className="required">*</span>
        </label>
        <select
          id="authType"
          value={authType}
          onChange={(e) => setAuthType(e.target.value as AuthType)}
          className="auth-select"
        >
          <option value="none">No Authentication</option>
          <option value="credentials">Login Credentials</option>
          <option value="bearer">Bearer Token</option>
          <option value="cookies">Cookies</option>
          <option value="localStorage">Local Storage</option>
        </select>
      </div>

      {/* Credentials Section */}
      {authType === 'credentials' && (
        <div className="auth-group">
          <div className="form-group">
            <label htmlFor="loginUrl">
              Login Page URL <span className="required">*</span>
            </label>
            <input
              type="text"
              id="loginUrl"
              value={credentials.url}
              onChange={(e) => setCredentials({ ...credentials, url: e.target.value })}
              placeholder="https://example.com/login"
              className="pl-10"
            />
          </div>
          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              className="pl-10"
              value={credentials.username}
              onChange={(e) => setCredentials({ ...credentials, username: e.target.value })}
            />
          </div>
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              className="pl-10"
              value={credentials.password}
              onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
            />
          </div>
          <div className="form-group">
            <label htmlFor="usernameSelector">Username Selector</label>
            <input
              type="text"
              id="usernameSelector"
              className="pl-10"
              value={credentials.usernameSelector}
              onChange={(e) => setCredentials({ ...credentials, usernameSelector: e.target.value })}
              placeholder="#username or .username-input"
            />
          </div>
          <div className="form-group">
            <label htmlFor="passwordSelector">Password Selector</label>
            <input
              type="text"
              id="passwordSelector"
              className="pl-10"
              value={credentials.passwordSelector}
              onChange={(e) => setCredentials({ ...credentials, passwordSelector: e.target.value })}
              placeholder="#password or .password-input"
            />
          </div>
          <div className="form-group">
            <label htmlFor="submitSelector">Submit Button Selector</label>
            <input
              type="text"
              id="submitSelector"
              className="pl-10"
              value={credentials.submitSelector}
              onChange={(e) => setCredentials({ ...credentials, submitSelector: e.target.value })}
              placeholder="#login-button or .submit-btn"
            />
          </div>
        </div>
      )}

      {/* Bearer Token Section */}
      {authType === 'bearer' && (
        <div className="auth-group">
          <h4>Bearer Token</h4>
          <div className="form-group">
            <input
              type="text"
              id="bearerToken"
              value={bearerToken}
              onChange={(e) => setBearerToken(e.target.value)}
              placeholder="Enter bearer token"
              className="pl-10"
            />
          </div>
        </div>
      )}

      {/* Cookies Section */}
      {authType === 'cookies' && (
        <div className="auth-group">
          <div className="section-header">
            <h4>Cookies</h4>
            <button
              type="button"
              onClick={handleAddCookie}
              className="btn-primary btn-sm"
              disabled={!canAddCookie()}
            >
              Add Cookie
            </button>
          </div>
          {cookies.map((cookie, index) => (
            <div key={index} className="cookie-item">
              <div className="cookie-row">
                <div className="form-group">
                  <label>Name</label>
                  <input
                    type="text"
                    value={cookie.name}
                    onChange={(e) => handleUpdateCookie(index, 'name', e.target.value)}
                    placeholder="Cookie name"
                    className="pl-10"
                  />
                </div>
                <div className="form-group">
                  <label>Value</label>
                  <input
                    type="text"
                    value={cookie.value}
                    onChange={(e) => handleUpdateCookie(index, 'value', e.target.value)}
                    placeholder="Cookie value"
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="cookie-row">
                <div className="form-group">
                  <label>URL</label>
                  <input
                    type="text"
                    value={cookie.url || ''}
                    onChange={(e) => handleUpdateCookie(index, 'url', e.target.value)}
                    placeholder="https://example.com"
                    className="pl-10"
                  />
                </div>
              </div>
              <button
                type="button"
                onClick={() => handleRemoveCookie(index)}
                className="btn-black btn-sm"
              >
                Remove Cookie
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Local Storage Section */}
      {authType === 'localStorage' && (
        <div className="auth-group">
          <div className="section-header">
            <h4>Local Storage</h4>
            <button
              type="button"
              onClick={handleAddLocalStorage}
              className="btn-primary btn-sm"
              disabled={!canAddLocalStorageItem()}
            >
              Add Local Storage Item
            </button>
          </div>
          {localStorage.map((item, index) => (
            <div key={index} className="local-storage-item">
              <div className="form-group">
                <input
                  type="text"
                  value={item.key}
                  onChange={(e) => handleUpdateLocalStorage(index, 'key', e.target.value)}
                  placeholder="Key"
                  className="pl-10"
                />
              </div>
              <div className="form-group">
                <input
                  type="text"
                  value={item.value}
                  onChange={(e) => handleUpdateLocalStorage(index, 'value', e.target.value)}
                  placeholder="Value"
                  className="pl-10"
                />
              </div>
              <button
                type="button"
                onClick={() => handleRemoveLocalStorage(index)}
                className="btn-black btn-sm"
              >
                Remove
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
