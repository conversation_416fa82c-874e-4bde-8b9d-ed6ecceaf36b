package org.enosis.automation.service.stream;

import com.google.gson.Gson;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.log4j.Log4j2;
import org.enosis.automation.domain.api.request.ApiRequest;
import org.enosis.automation.domain.api.response.PromptCompressionRequest;
import org.enosis.automation.domain.api.response.PromptCompressionResponse;
import org.enosis.automation.infrastructure.config.meter.Counted;
import org.enosis.automation.infrastructure.config.meter.Timed;
import org.enosis.automation.service.compression.CompressionService;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

@Log4j2
@Service
public class StreamingCodeGenerationService {

    private final CompressionService compressionService;
    private final Timer codeGenerationMethodTimer;

    @Value("${llm-lingua.llmlingua-2-xlm-roberta-large-meetingbank.compression.rate}")
    private Double compressionRate;

    public StreamingCodeGenerationService(CompressionService compressionService, MeterRegistry meterRegistry) {
        this.compressionService = compressionService;
        this.codeGenerationMethodTimer = Timer.builder("generateCode")
                .description("Code Generation Response Time")
                .tags("method", "generateCode")
                .register(meterRegistry);
    }

    @Counted(value = "generate.code.counter", tags = {"service", "StreamingCodeGenerationService"})
    public Flux<String> generate(ApiRequest request, ChatClient chatClient) {
        log.info("API Request : \n{}",new Gson().toJson(request));

        Instant startTime = Instant.now();

        StringBuilder sb = new StringBuilder();
        sb.append(request.getSystemCommand());

        return Mono.fromCallable(() -> {
                    PromptCompressionResponse response = getCompressionResponse(sb);
                    return (response != null ? response.getOutput_prompt() : "") + "\n" +
                            (request.getQuestion() != null ? request.getQuestion() : "");
                })
                .doOnNext((content) -> {
                    log.info("Before Going To LLM {}", content);
                })
                .subscribeOn(Schedulers.boundedElastic())
                .flatMapMany(prompt ->
                        chatClient.prompt(prompt).stream().content()
                                .onBackpressureBuffer(10, val -> log.info("Store Buffered Response: {}", val))
                                //.doOnNext(data -> log.info("Pushed Data to Client: {}", data))
                                .doOnComplete(() -> log.info("Streaming Completed"))
                                .doOnTerminate(() -> {
                                    Instant endTime = Instant.now();
                                    long processingTime = Duration.between(startTime, endTime).toSeconds();
                                    log.info("Processing Time : {}", processingTime);
                                    codeGenerationMethodTimer.record(processingTime, TimeUnit.SECONDS);
                                })
                )
                .doOnCancel(() -> log.info("Subscriber Cancelled Streaming."));

    }

    private PromptCompressionResponse getCompressionResponse(StringBuilder sb) {
        return compressionService.compress(
                PromptCompressionRequest.builder()
                        .input_prompt(sb.toString())
                        .rate(compressionRate)
                        .build()
        ).block();
    }


}