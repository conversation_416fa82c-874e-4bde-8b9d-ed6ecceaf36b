import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import './ErrorPage.css'; // We'll create this CSS file next

interface ErrorPageState {
  message?: string;
  status?: number;
}

const ErrorPage: React.FC = () => {
  const location = useLocation();
  // Attempt to get state passed during navigation, or provide defaults
  let { message , status  } =
    (location.state as ErrorPageState) || {};
  /// if no state, get from url
  if (!message && !status) {
    const searchParams = new URLSearchParams(location.search);
    message = searchParams.get('message') || '';
    status = parseInt(searchParams.get('status') || '500');
  }

  let title = 'Error';
  if (status === 404) {
    title = 'Data Not Found';
  } else if (status === 400) {
    title = 'Bad Request';
  } else if (status && status >= 500) {
    title = 'Server Error';
  } else {
    title = 'An unexpected error occurred.';
    message = 'Please try again later.';
    status = 500;
  }

  return (
    <div className="error-page-container">
      <h1>
        {status} - {title}
      </h1>
      <p>{message}</p>
      {/* Optionally add a link to go back or to the homepage */}
      <Link to="/">Go Home</Link>
    </div>
  );
};

export default ErrorPage;
