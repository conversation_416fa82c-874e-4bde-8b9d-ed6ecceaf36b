package com.enosisbd.api.server.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Entity
@Table(indexes = {
    @Index(name = "idx_project_updated_at", columnList = "updatedAt")
})
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Project extends BaseEntity {

    @NotNull(message = "Project name cannot be null")
    @NotBlank(message = "Project name cannot be empty")
    @Size(min = 3, max = 255, message = "Project name must be between 3 and 255 characters")
    @Column(nullable = false)
    private String name;

    @Column(columnDefinition = "TEXT")
    private String description;

    // Google Sheets metadata
    /**
     * The ID of the Google Sheet associated with this project.
     * This is extracted from the Google Sheet URL and used for API access.
     */
    @Column(name = "google_sheet_id")
    private String googleSheetId;

    /**
     * The URL of the Google Sheet associated with this project.
     */
    @Column(name = "google_sheet_url")
    private String googleSheetUrl;

    /**
     * The column letter (e.g., "A", "B") that contains submodule names in the Google Sheet.
     */
    @Column(name = "submodule_column")
    private String submoduleColumn;

    /**
     * The row number where submodule data starts in the Google Sheet (1-based).
     */
    @Column(name = "submodule_start_row")
    private Integer submoduleStartRow;

    /**
     * The email of the user who has access to the Google Sheet.
     * This is necessary for Google OAuth authentication when accessing the sheet.
     * The system uses this email to verify permissions and access the sheet's data
     * through the Google Sheets API.
     */
    @Column(name = "google_user_email")
    private String googleUserEmail;

    /**
     * The column letter (e.g., "A", "B") that contains case IDs in the Google Sheet.
     */
    @Column(name = "case_id_column")
    private String caseIdColumn;

    /**
     * The column letter (e.g., "A", "B") that contains test case descriptions in the Google Sheet.
     */
    @Column(name = "test_case_description_column")
    private String testCaseDescriptionColumn;

    /**
     * The column letter (e.g., "A", "B") that contains test case expected results in the Google Sheet.
     */
    @Column(name = "test_case_expected_result_column")
    private String testCaseExpectedResultColumn;

    @OneToMany(mappedBy = "project", cascade = CascadeType.REMOVE, orphanRemoval = true)
    private List<Module> modules;

    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL)
    private List<CrawledPage> crawledPages;
}
