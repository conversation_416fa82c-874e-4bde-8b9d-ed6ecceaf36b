import { TestScript } from '../types/automate-process.types';
import { RestResponse } from '../types/common.types';
import httpService from './http.service';

const API_ENDPOINTS = {
  TEST_SCRIPTS: '/api/test-scripts',
  TEST_SCRIPT: (id: number) => `/api/test-scripts/${id}`,
  TEST_SUITE_TEST_SCRIPT: (testSuiteId: number) => `/api/test-suites/${testSuiteId}/test-script`,
  SUBMODULE_TEST_SCRIPT: (submoduleId: number) => `/api/submodules/${submoduleId}/test-script`,
};

export const TestScriptService = {
  /**
   * Create a new test script
   */
  createTestScript: async (
    testScript: Omit<TestScript, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<TestScript> => {
    return (
      await httpService.post<RestResponse<TestScript>>(
        'common',
        API_ENDPOINTS.TEST_SCRIPTS,
        testScript,
      )
    ).data;
  },

  /**
   * Update an existing test script
   */
  updateTestScript: async (
    testScriptId: number,
    testScript: Partial<TestScript>,
  ): Promise<TestScript> => {
    return (
      await httpService.put<RestResponse<TestScript>>(
        'common',
        API_ENDPOINTS.TEST_SCRIPT(testScriptId),
        testScript,
      )
    ).data;
  },

  /**
   * Delete a test script
   */
  deleteTestScript: async (testScriptId: number): Promise<void> => {
    return httpService.delete('common', API_ENDPOINTS.TEST_SCRIPT(testScriptId));
  },

  /**
   * Get test script for a submodule
   */
  getBySubmoduleId: async (submoduleId: number): Promise<TestScript | null> => {
    try {
      const response = await httpService.get<RestResponse<TestScript>>(
        'common',
        API_ENDPOINTS.SUBMODULE_TEST_SCRIPT(submoduleId),
      );
      return response.data;
    } catch (error) {
      // Handle 404 by returning null instead of throwing
      if (error instanceof Error && error.message === 'Resource not found') {
        return null;
      }
      throw error;
    }
  },
};

export default TestScriptService;
