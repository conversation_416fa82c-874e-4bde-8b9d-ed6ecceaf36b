import React from 'react';
import './ActionButtons.css';
import { EntityType } from '../../types/entity.types';

// Generic type that can represent any entity (Project, Version, TestSuite, etc.)
type Entity = Record<string, any>;

type ActionButtonsProps<T extends Entity> = {
  // The entity object (project, version, testsuite, etc.)
  entity: T;
  // The type of entity (used for button titles and potentially other customizations)
  entityType: EntityType;
  // Optional property name to display in titles (e.g., "project.name")
  entityNameProperty?: string;
  // Callback functions
  onEdit: (entity: T, e: React.MouseEvent) => void;
  onDelete: (entity: T, e: React.MouseEvent) => void;
  // Optional share callback - only for Project and Version entities
  onShare?: (entity: T, e: React.MouseEvent) => void;
  // Flag to indicate if the entity is directly shared (not inherited)
  isDirectlyShared?: boolean;

  // Optional report callback function
  onReport?: (entity: T, e: React.MouseEvent) => void;
  // Flag to hide report button in actions (when shown elsewhere)
  hideReportInActions?: boolean;
  // Optional class name for additional styling
  className?: string;
};

const ActionButtons = <T extends Entity>({
  entity,
  entityType,
  entityNameProperty,
  onEdit,
  onDelete,
  onShare,
  isDirectlyShared = true,
  onReport,
  hideReportInActions = false,
  className = '',
}: ActionButtonsProps<T>) => {
  // Get entity name for title if entityNameProperty is provided
  const getEntityName = () => {
    if (!entityNameProperty) return '';

    // Handle nested properties with dot notation (e.g., "project.name")
    const properties = entityNameProperty.split('.');
    let value = entity;

    for (const prop of properties) {
      if (value && typeof value === 'object' && prop in value) {
        value = value[prop];
      } else {
        return '';
      }
    }

    return value ? ` "${value}"` : '';
  };

  return (
    <div className={`item-actions ${className}`} onClick={(e) => e.stopPropagation()}>
      {onShare && (entityType === 'Project' || entityType === 'Version') && isDirectlyShared && (
        <button
          type="button"
          className="action-button share"
          onClick={(e) => onShare(entity, e)}
          title={`Share ${entityType}${getEntityName()}`}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="18" cy="5" r="3"></circle>
            <circle cx="6" cy="12" r="3"></circle>
            <circle cx="18" cy="19" r="3"></circle>
            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
          </svg>
        </button>
      )}
      {onReport && !hideReportInActions && (
        <button
          className="action-button report"
          onClick={(e) => onReport(entity, e)}
          title={`View Report for ${entityType}${getEntityName()}`}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10 9 9 9 8 9"></polyline>
          </svg>
        </button>
      )}

      <button
        type="button"
        className="action-button edit"
        onClick={(e) => onEdit(entity, e)}
        title={`Edit ${entityType}${getEntityName()}`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M17 3a2.85 2.85 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path>
        </svg>
      </button>
      <button
        type="button"
        className="action-button delete"
        onClick={(e) => onDelete(entity, e)}
        title={`Delete ${entityType}${getEntityName()}`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M3 6h18"></path>
          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
        </svg>
      </button>
    </div>
  );
};

export default ActionButtons;
